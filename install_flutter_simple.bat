@echo off
echo ===============================================
echo         Install Flutter
echo ===============================================
echo.

echo Checking if Flutter is already installed...
flutter --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Flutter is already installed!
    flutter --version
    goto build_app
)

echo Flutter is not installed.
echo.

echo Creating Flutter directory...
if not exist "C:\flutter" (
    mkdir "C:\flutter"
    echo Created C:\flutter directory
)

echo.
echo Please follow these steps to install Flutter:
echo.
echo 1. Download Flutter from:
echo    https://docs.flutter.dev/get-started/install/windows
echo.
echo 2. Or use direct link:
echo    https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.24.5-stable.zip
echo.
echo 3. Extract to C:\flutter
echo.
echo 4. Add C:\flutter\bin to PATH:
echo    - Press Win + R, type: sysdm.cpl
echo    - Go to Advanced tab
echo    - Click Environment Variables
echo    - Find Path in System Variables
echo    - Click Edit, then New
echo    - Add: C:\flutter\bin
echo    - Click OK on all windows
echo.
echo 5. Restart Command Prompt
echo.
echo 6. Run: flutter doctor
echo.

echo Alternative: Use package manager
echo.
echo For Chocolatey users:
echo choco install flutter
echo.
echo For Scoop users:
echo scoop bucket add extras
echo scoop install flutter
echo.

:build_app
echo.
set /p continue="Do you want to build the app now? (Y/N): "
if /i "%continue%"=="y" (
    echo.
    echo Building app...
    cd "User app and web"
    flutter clean
    flutter pub get
    flutter build apk --release
    
    if exist "build\app\outputs\flutter-apk\app-release.apk" (
        echo.
        echo SUCCESS: APK built successfully!
        echo File location: build\app\outputs\flutter-apk\app-release.apk
        explorer "build\app\outputs\flutter-apk"
    ) else (
        echo ERROR: Failed to build APK
    )
)

pause
