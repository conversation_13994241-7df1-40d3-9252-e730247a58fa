{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98df1f79a5b4a38abc6e999887ad920eab", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986232ad7744228b3969321616754aa04d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b816944911141c0fb238e962c5b50343", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9835cb0b0385d9d21fccf23992325844aa", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b816944911141c0fb238e962c5b50343", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c412ea6075256588f33dcf48dfff704", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f4bd3dc064970a217a756f70053d2cf", "guid": "bfdfe7dc352907fc980b868725387e98b63e2b803fda1b17cd49578634850eff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983805029e7017fd9e738cb2f8e42db9fd", "guid": "bfdfe7dc352907fc980b868725387e981bab67496858ca9a4218c8655b5cfdfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d2184452dccdb205608a4321b07e6ce", "guid": "bfdfe7dc352907fc980b868725387e98103d40578ee3b41faa61e9a744fbe44d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f598ef56eb5d8420bdc36b9cda4b7cc", "guid": "bfdfe7dc352907fc980b868725387e986b9610774f6118ca7f3ada2729312636"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843d31734bcc231877d9c8e5d301cb5c7", "guid": "bfdfe7dc352907fc980b868725387e9835620f3ca1edd98fc47b02b67577e42e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847e5a9bb3e3b3c5131d2bd895cb1a2fc", "guid": "bfdfe7dc352907fc980b868725387e9838d671caa7593f661fec1f7a1569952b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d354b1d56665360a161375ec52c40d0", "guid": "bfdfe7dc352907fc980b868725387e98aa7e9137d8c415462f3c95d17c13e42c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e1c49d418c78d2fa0aa4f2592b50465", "guid": "bfdfe7dc352907fc980b868725387e9896857a9311b1bc7a4c3810afa042dc09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bce0c6e9334b0ef22722d6cefc8ef2c7", "guid": "bfdfe7dc352907fc980b868725387e98484baf01f5718bc3a2724fd3b6fc5cf3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b5fd74b24c1db062e7a57474470ebdf", "guid": "bfdfe7dc352907fc980b868725387e9872e02198344c264e2f43711ba023f488"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab81bf281f592782f97d0ab871010499", "guid": "bfdfe7dc352907fc980b868725387e98033f7379a20bbda33f99ff2144d0261e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854c89b65c19bda38870af39428f8e429", "guid": "bfdfe7dc352907fc980b868725387e987d145bce4aedca23efa280e6742f91d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a31e0ddf3547903b2bba225661567ee", "guid": "bfdfe7dc352907fc980b868725387e9805beb896ee0d5c259a22ed73748f0305"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980abf11ae66b5bfc0afc5dccbd64f93a0", "guid": "bfdfe7dc352907fc980b868725387e985a2d7ff6cf64ff2b559d5ab6ae6c9ac8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980500886de94b5a00815a88bffc3c2f47", "guid": "bfdfe7dc352907fc980b868725387e98211f0735eab8cb3188fadabdf36213b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1bba16308738e96ca9a824a46b56c9f", "guid": "bfdfe7dc352907fc980b868725387e987e06657b0c50ee962340624e99ca8fc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1d05f7856183595928e04e5fc07adc8", "guid": "bfdfe7dc352907fc980b868725387e980ee67bf2ef0f7da32c808d0aa2095aa8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836dde52c4e29d18cd5229b9d28d11b77", "guid": "bfdfe7dc352907fc980b868725387e98e752550c20a4f6519ee7867cb445a702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838f1941fc9a04b0d970ec7305c612c92", "guid": "bfdfe7dc352907fc980b868725387e98952568a9cda99da75f23c15cd285f7c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c60fcafe84828b5b5d5196f6b317ad4", "guid": "bfdfe7dc352907fc980b868725387e980a76060ab5680e300ee3e9c9215be019"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812796b234207ce811638683b911b59d6", "guid": "bfdfe7dc352907fc980b868725387e98451dfded96b6a03e78c5994f1a1b4794"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982eced7fec3f278fb785395b34d12d651", "guid": "bfdfe7dc352907fc980b868725387e9887d4f9a3b064bcd40dd8136c63acfc9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985885b4ff17eda38e1ed76c30d0403e5d", "guid": "bfdfe7dc352907fc980b868725387e98161507157d2870d4b17dc497acab1cb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fce0f6022e6e17932ba2d68b31fda946", "guid": "bfdfe7dc352907fc980b868725387e98cdf677d91713a8e9af9c5b991a5ec051"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822d41e371dde5ca38442eeb4407aaad1", "guid": "bfdfe7dc352907fc980b868725387e984041d37088ee823efa0a9d51c299c8d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98072e33552a91e5ca847cff70ce1e7a8b", "guid": "bfdfe7dc352907fc980b868725387e980a8b1e70b56a97aa2027404706f5b7bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d5dd764b531d6dc531ee92af785cb6a", "guid": "bfdfe7dc352907fc980b868725387e98731bb6beb2e9bf504b9d22a43d4e6f2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcf67944a871424ca3e6bc03ce9fc4c3", "guid": "bfdfe7dc352907fc980b868725387e98da9156a9f03a12446a2f6ea7b843df53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6c468ea999f1b48f99bd3b899c1590a", "guid": "bfdfe7dc352907fc980b868725387e98ad64de2d261d6c8b0ebecea4c273e335"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837691c7223c977cc3f8023627d9a10d3", "guid": "bfdfe7dc352907fc980b868725387e98f3d9d801cfd0a3243d94b92bb8303406"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e088fc037dc4d8cc5f9b75b0d5fd829", "guid": "bfdfe7dc352907fc980b868725387e98a0ee95caa73ae913afd18b5a1cf1da9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981969dfab0f6e9b93db3a187037490b9f", "guid": "bfdfe7dc352907fc980b868725387e985cb3d22e9273393af4eefa23f7338470"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac089d293756fdbe38d0dcfe540d54d4", "guid": "bfdfe7dc352907fc980b868725387e98f745ffbaec1def4d10bb1ccc40765bea"}], "guid": "bfdfe7dc352907fc980b868725387e988866f0f637d2f837b28531775e1e66c7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9858852c59424530bf8ebe6013d2a48897", "guid": "bfdfe7dc352907fc980b868725387e98e27516a693bd0c460071cf84737d589c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f38a724daa353daa08a27aa56d1b617", "guid": "bfdfe7dc352907fc980b868725387e9895cbd735a18e6b550432e1e1b553a16f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efc9e1e907e9fca084d24666e0d32d37", "guid": "bfdfe7dc352907fc980b868725387e98bedb8ee5bc60bc5a4bdc3b47389fb982"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899e0c33e809487686906dcd3551a6ab1", "guid": "bfdfe7dc352907fc980b868725387e9880a1a5f3595c0de3b1b16f54d0a8e191"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986648eda6536a639699a3274efb914d65", "guid": "bfdfe7dc352907fc980b868725387e9842748806b140ca0a862973798220403b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a87913d224e7c652e0cb3ee6a5ce02e", "guid": "bfdfe7dc352907fc980b868725387e987c16d85eb67f7ff2e321f0bd178a5a64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862dfdc1f968ba0cc2789e86c2d19e38d", "guid": "bfdfe7dc352907fc980b868725387e98a2e853989837a631bd959e00dd8535a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0bdae7de9c4f0fe6f4944a1c28958cb", "guid": "bfdfe7dc352907fc980b868725387e98bea89390c37cd4cb36bd46c1e7d3facc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c073d195d6906ae8560c66d5275e3f2", "guid": "bfdfe7dc352907fc980b868725387e980e98b44603ccda092224fbaefe8cc7d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988305c4363c31899fe9ede0d635afb589", "guid": "bfdfe7dc352907fc980b868725387e98600edb496405e00263a43bbc7d1d7a3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e80dedc2210e4ccf01d531560cd70a8", "guid": "bfdfe7dc352907fc980b868725387e989c7257bec019fc3b22514d247fc8be8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d048e4649ba978439e05fb0cb533cd4", "guid": "bfdfe7dc352907fc980b868725387e98ad4c3d69da2da29900a6cfcd0b2d9fe8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987421c18447c5bc7af2819c2eb28634d7", "guid": "bfdfe7dc352907fc980b868725387e98e939fc2df4f2a7428b720b3646fd78cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98885f207fbcc7f2f523c9a727423ef45c", "guid": "bfdfe7dc352907fc980b868725387e98193f3bb66a1e6843bfa9994c18b3ef2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816a59b82facc26c0bdbd2b35bef5b094", "guid": "bfdfe7dc352907fc980b868725387e9879523733f67c4796ba4fd6bc583f7893"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a99a1640014417b7df204bd791cc4ca2", "guid": "bfdfe7dc352907fc980b868725387e986e69b6b6e9e24707efd787082de396de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0c6fb07b7d6ca9cb8b4c530a960585c", "guid": "bfdfe7dc352907fc980b868725387e98e1bd81ae51a863e40e41edbecabaf23a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efe8c3712f17a3305a87bc821284cc76", "guid": "bfdfe7dc352907fc980b868725387e98fee7b4cd2d46f900653de7f05bd33f37"}], "guid": "bfdfe7dc352907fc980b868725387e982c6498ab0d4e72133bbb733b82dffff8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c7bf7ee5fbc558c481104fe48eb4be0", "guid": "bfdfe7dc352907fc980b868725387e986c9ba9766971b927e8cb8194d3eff005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879361b2ec1c99c8df06dcafa1900307d", "guid": "bfdfe7dc352907fc980b868725387e98cb3503e9ed90208c9c1f243bdc43d442"}], "guid": "bfdfe7dc352907fc980b868725387e98a0d110138077d030f339dcdcd460ee10", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a74e96b4211558608e72780c7d501f4b", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98589c24c5dc7e0a190c2d84f2e5a10848", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}