# دليل التحكم في التطبيق من لوحة الإدارة
# Mobile App Control Guide

## 🎛️ كيفية التحكم في التطبيق من الموقع

### 🔐 الدخول إلى لوحة التحكم:
```
https://www.zainalabidin.pro/admin
```

---

## 📱 إعدادات التطبيق الأساسية

### 1. **إعدادات العمل (Business Settings)**
📍 **المسار:** Settings > Business Information

**ما يمكن تعديله:**
- ✅ اسم التطبيق
- ✅ شعار التطبيق
- ✅ وصف التطبيق
- ✅ معلومات الاتصال
- ✅ العنوان
- ✅ رقم الهاتف
- ✅ البريد الإلكتروني

### 2. **إعدادات اللغة (Language Settings)**
📍 **المسار:** Settings > Language Setup

**ما يمكن تعديله:**
- ✅ تفعيل/إلغاء اللغات
- ✅ تعيين اللغة الافتراضية
- ✅ ترجمة النصوص
- ✅ اتجاه النص (RTL/LTR)

### 3. **إعدادات العملة (Currency Settings)**
📍 **المسار:** Settings > Business Information

**ما يمكن تعديله:**
- ✅ نوع العملة
- ✅ رمز العملة
- ✅ موضع رمز العملة
- ✅ عدد الخانات العشرية

---

## 🛍️ إدارة المحتوى

### 1. **الفئات (Categories)**
📍 **المسار:** Service Management > Categories

**ما يمكن عمله:**
- ✅ إضافة فئات جديدة
- ✅ تعديل الفئات الموجودة
- ✅ رفع صور للفئات
- ✅ ترتيب الفئات
- ✅ تفعيل/إلغاء الفئات

### 2. **الخدمات (Services)**
📍 **المسار:** Service Management > Services

**ما يمكن عمله:**
- ✅ إضافة خدمات جديدة
- ✅ تحديد أسعار الخدمات
- ✅ رفع صور للخدمات
- ✅ كتابة وصف مفصل
- ✅ تحديد مدة الخدمة
- ✅ إدارة المتغيرات (Variants)

### 3. **البانرات (Banners)**
📍 **المسار:** Promotion Management > Banners

**ما يمكن عمله:**
- ✅ إضافة بانرات إعلانية
- ✅ ربط البانرات بالخدمات
- ✅ تحديد مدة العرض
- ✅ ترتيب البانرات

### 4. **العروض والكوبونات (Offers & Coupons)**
📍 **المسار:** Promotion Management > Coupons

**ما يمكن عمله:**
- ✅ إنشاء كوبونات خصم
- ✅ تحديد نسبة/مبلغ الخصم
- ✅ تحديد مدة صلاحية الكوبون
- ✅ تحديد شروط الاستخدام

---

## 👥 إدارة المستخدمين

### 1. **العملاء (Customers)**
📍 **المسار:** User Management > Customers

**ما يمكن عمله:**
- ✅ عرض قائمة العملاء
- ✅ تعديل بيانات العملاء
- ✅ حظر/إلغاء حظر العملاء
- ✅ عرض تاريخ الطلبات

### 2. **مقدمي الخدمات (Providers)**
📍 **المسار:** Provider Management > Providers

**ما يمكن عمله:**
- ✅ إضافة مقدمي خدمات جدد
- ✅ الموافقة على طلبات الانضمام
- ✅ إدارة مناطق الخدمة
- ✅ تحديد العمولات

---

## 📊 التقارير والإحصائيات

### 1. **لوحة المعلومات (Dashboard)**
📍 **المسار:** Dashboard

**ما يمكن مشاهدته:**
- ✅ إجمالي الطلبات
- ✅ إجمالي الإيرادات
- ✅ عدد العملاء الجدد
- ✅ أداء مقدمي الخدمات

### 2. **تقارير مفصلة (Reports)**
📍 **المسار:** Reports

**ما يمكن مشاهدته:**
- ✅ تقارير المبيعات
- ✅ تقارير العملاء
- ✅ تقارير مقدمي الخدمات
- ✅ تقارير الخدمات الأكثر طلباً

---

## 🔧 إعدادات متقدمة

### 1. **إعدادات الدفع (Payment Settings)**
📍 **المسار:** Settings > Payment Methods

**ما يمكن تعديله:**
- ✅ تفعيل طرق الدفع
- ✅ إعدادات البوابات الإلكترونية
- ✅ الدفع عند الاستلام
- ✅ المحفظة الإلكترونية

### 2. **إعدادات الإشعارات (Notification Settings)**
📍 **المسار:** Settings > Notification Settings

**ما يمكن تعديله:**
- ✅ إعدادات Firebase
- ✅ قوالب الإشعارات
- ✅ إشعارات البريد الإلكتروني
- ✅ إشعارات SMS

### 3. **إعدادات التطبيق (App Settings)**
📍 **المسار:** Settings > App Settings

**ما يمكن تعديله:**
- ✅ إصدار التطبيق المطلوب
- ✅ رسائل الصيانة
- ✅ إعدادات الخريطة
- ✅ إعدادات المشاركة

---

## 🚀 خطوات البدء السريع

### 1. **إعداد المعلومات الأساسية:**
1. اذهب إلى Settings > Business Information
2. أدخل اسم شركتك وشعارها
3. أضف معلومات الاتصال

### 2. **إضافة الفئات والخدمات:**
1. اذهب إلى Service Management > Categories
2. أضف الفئات الرئيسية
3. اذهب إلى Services وأضف الخدمات

### 3. **إعداد طرق الدفع:**
1. اذهب إلى Settings > Payment Methods
2. فعل الطرق المطلوبة
3. أدخل بيانات البوابات الإلكترونية

### 4. **تخصيص التطبيق:**
1. اذهب إلى Settings > App Settings
2. رفع الشعارات والصور
3. تخصيص الألوان والثيم

---

## 📱 اختبار التطبيق

### بعد إجراء أي تغييرات:
1. **احفظ التغييرات في لوحة التحكم**
2. **أعد تشغيل التطبيق**
3. **اختبر الوظائف الجديدة**

### للتأكد من التحديثات:
- اسحب للأسفل في التطبيق لتحديث البيانات
- أو أعد تشغيل التطبيق

---

## 🎯 نصائح مهمة

### ✅ افعل:
- احفظ نسخة احتياطية قبل التغييرات الكبيرة
- اختبر التغييرات في بيئة التطوير أولاً
- استخدم صور عالية الجودة
- اكتب أوصاف واضحة للخدمات

### ❌ لا تفعل:
- تغيير إعدادات قاعدة البيانات بدون خبرة
- حذف البيانات المهمة
- تعديل ملفات النظام مباشرة
- تجاهل رسائل الخطأ

---

## 📞 الدعم الفني

إذا واجهت أي مشاكل:
1. تحقق من logs في لوحة التحكم
2. راجع إعدادات API
3. تأكد من اتصال التطبيق بالإنترنت
4. أعد تشغيل التطبيق

**الآن يمكنك التحكم الكامل في التطبيق من لوحة الإدارة!** 🎉
