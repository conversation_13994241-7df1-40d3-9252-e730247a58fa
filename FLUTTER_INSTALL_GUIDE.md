# دليل تثبيت Flutter
# Flutter Installation Guide

## 🚨 المشكلة الحالية:
Flutter غير مثبت على النظام، لذلك لا يمكن بناء التطبيق.

---

## 🔧 الحلول المتاحة:

### الحل الأول: التثبيت التلقائي (الأسرع)
```bash
# إذا كان Git مثبت
auto_install_flutter.bat

# أو
install_flutter.bat
```

### الحل الثاني: التثبيت اليدوي (الأكثر أماناً)

#### الخطوة 1: تحميل Flutter
1. **اذهب إلى:** https://docs.flutter.dev/get-started/install/windows
2. **حمل ملف ZIP** (حوالي 1.5 GB)
3. **أو استخدم الرابط المباشر:**
   ```
   https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.24.5-stable.zip
   ```

#### الخطوة 2: استخراج الملفات
1. **استخرج الملفات** إلى `C:\flutter`
2. **تأكد من المسار:** `C:\flutter\bin\flutter.exe`

#### الخطوة 3: إضافة Flutter إلى PATH
1. **اضغط** `Win + R` واكتب `sysdm.cpl`
2. **اذهب إلى** تبويب `Advanced`
3. **اضغط** `Environment Variables`
4. **في System Variables** ابحث عن `Path`
5. **اضغط** `Edit` ثم `New`
6. **أضف:** `C:\flutter\bin`
7. **اضغط** `OK` على جميع النوافذ

#### الخطوة 4: إعادة تشغيل Command Prompt
1. **أغلق** جميع نوافذ Command Prompt
2. **افتح** Command Prompt جديد
3. **اكتب:** `flutter --version`

---

## 🛠️ الحل الثالث: استخدام Package Managers

### باستخدام Chocolatey:
```bash
# تثبيت Chocolatey أولاً (إذا لم يكن مثبت)
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# ثم تثبيت Flutter
choco install flutter
```

### باستخدام Scoop:
```bash
# تثبيت Scoop أولاً (إذا لم يكن مثبت)
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
irm get.scoop.sh | iex

# ثم تثبيت Flutter
scoop bucket add extras
scoop install flutter
```

---

## ✅ التحقق من التثبيت

### بعد التثبيت، شغل هذه الأوامر:
```bash
# فحص إصدار Flutter
flutter --version

# فحص شامل للنظام
flutter doctor

# فحص الأجهزة المتصلة
flutter devices
```

### النتيجة المتوقعة:
```
Flutter 3.24.5 • channel stable
Tools • Dart 3.5.4 • DevTools 2.37.3
```

---

## 🔧 إصلاح المشاكل الشائعة

### مشكلة: "flutter is not recognized"
**الحل:**
1. تأكد من إضافة `C:\flutter\bin` إلى PATH
2. أعد تشغيل Command Prompt
3. جرب المسار الكامل: `C:\flutter\bin\flutter.exe --version`

### مشكلة: "Android toolchain not found"
**الحل:**
1. ثبت Android Studio
2. شغل: `flutter doctor --android-licenses`
3. اقبل جميع التراخيص

### مشكلة: "No connected devices"
**الحل:**
1. وصل هاتف Android عبر USB
2. فعل USB Debugging
3. أو استخدم Android Emulator

---

## 🚀 بعد تثبيت Flutter

### الخطوة 1: بناء التطبيق
```bash
cd "التطبيق\User app and web"
flutter clean
flutter pub get
flutter build apk --release
```

### الخطوة 2: العثور على APK
```
التطبيق\User app and web\build\app\outputs\flutter-apk\app-release.apk
```

### الخطوة 3: تثبيت على الهاتف
1. انسخ APK إلى الهاتف
2. فعل "Unknown Sources"
3. ثبت التطبيق

---

## 📋 متطلبات إضافية

### للتطوير الكامل:
- ✅ **Flutter SDK** (مطلوب)
- ✅ **Android Studio** (مُنصح به)
- ✅ **VS Code** (اختياري)
- ✅ **Git** (مُنصح به)

### للبناء فقط:
- ✅ **Flutter SDK** (مطلوب فقط)

---

## 🎯 الخطوات السريعة

### إذا كنت تريد الحل الأسرع:

1. **شغل:**
   ```bash
   auto_install_flutter.bat
   ```

2. **انتظر حتى ينتهي التحميل**

3. **أعد تشغيل Command Prompt**

4. **شغل:**
   ```bash
   build_apk.bat
   ```

5. **ستحصل على APK جاهز!**

---

## 📞 إذا واجهت مشاكل

### جرب هذا الترتيب:
1. `install_flutter.bat` - للتثبيت اليدوي
2. `auto_install_flutter.bat` - للتثبيت التلقائي
3. التثبيت اليدوي من الموقع الرسمي

### أو أخبرني:
- نوع الخطأ الذي تواجهه
- نتيجة `flutter doctor`
- إصدار Windows

**الهدف: الحصول على APK يعمل مع موقعك!** 🎯
