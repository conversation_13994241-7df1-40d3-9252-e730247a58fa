@echo off
chcp 65001 >nul
echo ===============================================
echo        تحديث إعدادات التطبيق
echo     Mobile App Configuration Update
echo ===============================================
echo.

REM التحقق من وجود ملف app_constants.dart
set "APP_CONSTANTS_FILE=التطبيق\User app and web\lib\utils\app_constants.dart"
set "ANDROID_MANIFEST_FILE=التطبيق\User app and web\android\app\src\main\AndroidManifest.xml"

if not exist "%APP_CONSTANTS_FILE%" (
    echo ❌ ملف app_constants.dart غير موجود
    echo تأكد من أن مجلد التطبيق في المكان الصحيح
    echo المسار المتوقع: %APP_CONSTANTS_FILE%
    pause
    exit /b 1
)

echo ✅ تم العثور على ملفات التطبيق
echo.

echo يرجى إدخال المعلومات التالية:
echo.

REM طلب رابط الموقع
set /p WEBSITE_URL="🌐 أدخل رابط موقعك (مثال: https://yourdomain.com): "

if "%WEBSITE_URL%"=="" (
    echo ❌ يجب إدخال رابط الموقع
    pause
    exit /b 1
)

REM إزالة / من نهاية الرابط إذا وجدت
if "%WEBSITE_URL:~-1%"=="/" (
    set "WEBSITE_URL=%WEBSITE_URL:~0,-1%"
)

REM طلب اسم التطبيق (اختياري)
set /p APP_NAME="📱 أدخل اسم التطبيق (اتركه فارغاً للاحتفاظ بالاسم الحالي): "

REM طلب Google Maps API Key (اختياري)
set /p MAPS_KEY="🗺️ أدخل Google Maps API Key (اتركه فارغاً لتخطي): "

echo.
echo بدء تحديث الإعدادات...
echo.

REM تحديث baseUrl باستخدام PowerShell
echo تحديث رابط الموقع...
powershell -Command "(Get-Content '%APP_CONSTANTS_FILE%') -replace \"static const String baseUrl = '[^']*';\", \"static const String baseUrl = '%WEBSITE_URL%';\" | Set-Content '%APP_CONSTANTS_FILE%'"

if %errorlevel% equ 0 (
    echo ✅ تم تحديث رابط الموقع بنجاح
) else (
    echo ❌ فشل في تحديث رابط الموقع
)

REM تحديث اسم التطبيق إذا تم إدخاله
if not "%APP_NAME%"=="" (
    echo تحديث اسم التطبيق...
    powershell -Command "(Get-Content '%APP_CONSTANTS_FILE%') -replace \"static const String appName = '[^']*';\", \"static const String appName = '%APP_NAME%';\" | Set-Content '%APP_CONSTANTS_FILE%'"
    
    if %errorlevel% equ 0 (
        echo ✅ تم تحديث اسم التطبيق بنجاح
    ) else (
        echo ❌ فشل في تحديث اسم التطبيق
    )
)

REM تحديث Google Maps API Key إذا تم إدخاله
if not "%MAPS_KEY%"=="" (
    if exist "%ANDROID_MANIFEST_FILE%" (
        echo تحديث Google Maps API Key...
        powershell -Command "(Get-Content '%ANDROID_MANIFEST_FILE%') -replace 'android:value=\"YOUR_MAP_KEY\"', 'android:value=\"%MAPS_KEY%\"' | Set-Content '%ANDROID_MANIFEST_FILE%'"
        
        if %errorlevel% equ 0 (
            echo ✅ تم تحديث Google Maps API Key بنجاح
        ) else (
            echo ❌ فشل في تحديث Google Maps API Key
        )
    ) else (
        echo ⚠️ ملف AndroidManifest.xml غير موجود
    )
)

echo.
echo 🎉 تم تحديث إعدادات التطبيق!
echo.
echo الخطوات التالية:
echo 1. تأكد من أن موقعك يعمل على الرابط: %WEBSITE_URL%
echo 2. تأكد من تفعيل HTTPS
echo 3. تأكد من تفعيل CORS في الموقع
echo 4. اختبر التطبيق: flutter run
echo 5. ابني التطبيق للإنتاج: flutter build apk --release
echo.

REM عرض ملخص التغييرات
echo ملخص التغييرات:
echo - رابط الموقع: %WEBSITE_URL%
if not "%APP_NAME%"=="" echo - اسم التطبيق: %APP_NAME%
if not "%MAPS_KEY%"=="" echo - Google Maps API Key: تم التحديث

echo.
echo ===============================================
pause
