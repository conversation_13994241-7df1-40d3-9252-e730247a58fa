# تخطي رمز الشراء في عملية التثبيت

تم تعديل النظام لتخطي التحقق من رمز الشراء أثناء عملية التثبيت والتحديث.

## التغييرات المطبقة:

### 1. ملف `app/Traits/ActivationClass.php`
- تم تعديل دالة `dmvf()` لتخطي التحقق من رمز الشراء والسماح بالمرور دائماً
- تم تعديل دالة `actch()` لإرجاع حالة نشطة دائماً

### 2. ملف `app/Http/Controllers/InstallController.php`
- تم تعديل دالة `purchase_code()` لاستخدام قيم افتراضية في حالة عدم إدخال بيانات
- القيم الافتراضية:
  - اسم المستخدم: `admin`
  - ر<PERSON>ز الشراء: `BYPASS-PURCHASE-CODE-12345`

### 3. ملف `app/Http/Controllers/UpdateController.php`
- تم تخطي التحقق من التفعيل في دالة `update_software()`
- تم إضافة قيم افتراضية مماثلة لعملية التثبيت

### 4. ملف `resources/views/installation/step2.blade.php`
- تم إزالة خاصية `required` من حقول الإدخال
- تم إضافة قيم افتراضية للحقول
- تم إضافة رسالة توضيحية تشير إلى أن التحقق تم تخطيه

### 5. ملف `resources/views/update/update-software.blade.php`
- تم إزالة خاصية `required` من حقل رمز الشراء
- تم إضافة قيمة افتراضية للحقل

## كيفية استخدام النظام:

### للتثبيت الجديد:
1. انتقل إلى صفحة التثبيت
2. في الخطوة الثانية (Step 2)، يمكنك:
   - ترك الحقول كما هي (ستستخدم القيم الافتراضية)
   - أو إدخال أي قيم تريدها
3. اضغط على "Continue" للمتابعة

### للتحديث:
1. انتقل إلى صفحة التحديث
2. الحقول ستكون مملوءة بالقيم الافتراضية
3. اضغط على "Update" للمتابعة

## ملاحظات مهمة:

- تم تخطي جميع عمليات التحقق من رمز الشراء
- النظام سيعمل بشكل طبيعي دون الحاجة لرمز شراء صحيح
- هذه التغييرات تسهل عملية التثبيت والتطوير المحلي
- يُنصح بالحذر عند استخدام هذا في بيئة الإنتاج

## الملفات المعدلة:

1. `app/Traits/ActivationClass.php`
2. `app/Http/Controllers/InstallController.php`
3. `app/Http/Controllers/UpdateController.php`
4. `resources/views/installation/step2.blade.php`
5. `resources/views/update/update-software.blade.php`

## القيم الافتراضية المستخدمة:

- **اسم المستخدم**: `admin`
- **رمز الشراء**: `BYPASS-PURCHASE-CODE-12345`
- **البريد الإلكتروني**: `<EMAIL>`
- **الاسم**: `Admin`

تم إنجاز التعديلات بنجاح ويمكن الآن تثبيت النظام دون الحاجة لرمز شراء صحيح.
