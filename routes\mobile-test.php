<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Mobile App Test Routes
|--------------------------------------------------------------------------
| هذه routes للاختبار فقط - تأكد من أن التطبيق يتصل بالموقع بشكل صحيح
*/

// اختبار أساسي للاتصال
Route::get('/mobile-test', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'التطبيق متصل بالموقع بنجاح!',
        'website' => 'https://www.zainalabidin.pro',
        'timestamp' => now(),
        'app_name' => config('app.name'),
        'app_url' => config('app.url'),
        'cors_enabled' => true,
    ]);
});

// اختبار CORS
Route::get('/mobile-cors-test', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'CORS يعمل بشكل صحيح',
        'cors_enabled' => true,
        'allowed_origins' => '*',
        'timestamp' => now(),
    ])->header('Access-Control-Allow-Origin', '*')
      ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
      ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
});

// اختبار قاعدة البيانات
Route::get('/mobile-db-test', function () {
    try {
        $pdo = DB::connection()->getPdo();
        return response()->json([
            'status' => 'success',
            'message' => 'قاعدة البيانات متصلة بنجاح',
            'database_connected' => true,
            'timestamp' => now(),
        ]);
    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => 'خطأ في الاتصال بقاعدة البيانات',
            'database_connected' => false,
            'error' => $e->getMessage(),
            'timestamp' => now(),
        ], 500);
    }
});

// اختبار إعدادات التطبيق
Route::get('/mobile-config-test', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'إعدادات التطبيق',
        'config' => [
            'app_name' => config('app.name'),
            'app_url' => config('app.url'),
            'app_env' => config('app.env'),
            'app_debug' => config('app.debug'),
            'timezone' => config('app.timezone'),
            'locale' => config('app.locale'),
        ],
        'timestamp' => now(),
    ]);
});

// اختبار اللغة العربية
Route::get('/mobile-arabic-test', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'اختبار اللغة العربية',
        'arabic_text' => 'مرحباً بك في تطبيق زين العابدين',
        'english_text' => 'Welcome to Zain Al-Abidin App',
        'rtl_support' => true,
        'timestamp' => now(),
    ]);
});
