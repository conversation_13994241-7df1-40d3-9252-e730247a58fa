@echo off
chcp 65001 >nul
echo ===============================================
echo        اختبار اتصال التطبيق بالموقع
echo      Mobile App Connection Test
echo ===============================================
echo.

echo 🌐 الموقع: https://www.zainalabidin.pro
echo 📱 التطبيق: User app and web
echo.

echo اختبار الاتصالات...
echo.

REM اختبار الاتصال الأساسي
echo 1️⃣ اختبار الاتصال الأساسي...
curl -s -o nul -w "%%{http_code}" https://www.zainalabidin.pro/api/mobile-test > temp_result.txt
set /p RESULT=<temp_result.txt
if "%RESULT%"=="200" (
    echo ✅ الاتصال الأساسي يعمل
) else (
    echo ❌ فشل الاتصال الأساسي - كود: %RESULT%
)
del temp_result.txt

echo.

REM اختبار CORS
echo 2️⃣ اختبار CORS...
curl -s -o nul -w "%%{http_code}" https://www.zainalabidin.pro/api/mobile-cors-test > temp_result.txt
set /p RESULT=<temp_result.txt
if "%RESULT%"=="200" (
    echo ✅ CORS يعمل بشكل صحيح
) else (
    echo ❌ مشكلة في CORS - كود: %RESULT%
)
del temp_result.txt

echo.

REM اختبار قاعدة البيانات
echo 3️⃣ اختبار قاعدة البيانات...
curl -s -o nul -w "%%{http_code}" https://www.zainalabidin.pro/api/mobile-db-test > temp_result.txt
set /p RESULT=<temp_result.txt
if "%RESULT%"=="200" (
    echo ✅ قاعدة البيانات متصلة
) else (
    echo ❌ مشكلة في قاعدة البيانات - كود: %RESULT%
)
del temp_result.txt

echo.

REM اختبار الإعدادات
echo 4️⃣ اختبار الإعدادات...
curl -s -o nul -w "%%{http_code}" https://www.zainalabidin.pro/api/mobile-config-test > temp_result.txt
set /p RESULT=<temp_result.txt
if "%RESULT%"=="200" (
    echo ✅ إعدادات التطبيق صحيحة
) else (
    echo ❌ مشكلة في الإعدادات - كود: %RESULT%
)
del temp_result.txt

echo.

REM اختبار اللغة العربية
echo 5️⃣ اختبار اللغة العربية...
curl -s -o nul -w "%%{http_code}" https://www.zainalabidin.pro/api/mobile-arabic-test > temp_result.txt
set /p RESULT=<temp_result.txt
if "%RESULT%"=="200" (
    echo ✅ اللغة العربية مدعومة
) else (
    echo ❌ مشكلة في اللغة العربية - كود: %RESULT%
)
del temp_result.txt

echo.
echo ===============================================
echo 📋 ملخص الاختبارات:
echo.
echo إذا كانت جميع الاختبارات ✅ فالتطبيق جاهز!
echo إذا كان هناك ❌ راجع إعدادات الموقع
echo.
echo 🚀 الخطوة التالية:
echo cd "التطبيق\User app and web"
echo flutter pub get
echo flutter run
echo.
echo ===============================================
pause
