@echo off
echo ===============================================
echo         Run Mobile App
echo ===============================================
echo.

echo App is connected to: https://www.zainalabidin.pro
echo.

echo Step 1: Check Flutter installation...
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed!
    echo Please install Flutter first from: https://flutter.dev
    pause
    exit /b 1
)

echo Flutter is installed!
echo.

echo Step 2: Navigate to app directory...
cd "التطبيق\User app and web"
if %errorlevel% neq 0 (
    echo ERROR: Cannot find app directory
    pause
    exit /b 1
)

echo.
echo Step 3: Clean and get dependencies...
flutter clean
flutter pub get

echo.
echo Step 4: Check connected devices...
flutter devices

echo.
echo ===============================================
echo Choose option:
echo.
echo 1. Run on device/emulator
echo 2. Build APK for testing
echo 3. Build APK for production
echo 4. Run on web browser
echo 5. Exit
echo.
set /p choice="Enter choice (1-5): "

if "%choice%"=="1" (
    echo.
    echo Running app on device...
    flutter run
) else if "%choice%"=="2" (
    echo.
    echo Building debug APK...
    flutter build apk --debug
    echo.
    echo APK created at: build\app\outputs\flutter-apk\app-debug.apk
    explorer "build\app\outputs\flutter-apk"
) else if "%choice%"=="3" (
    echo.
    echo Building release APK...
    flutter build apk --release
    echo.
    echo APK created at: build\app\outputs\flutter-apk\app-release.apk
    explorer "build\app\outputs\flutter-apk"
) else if "%choice%"=="4" (
    echo.
    echo Running on web...
    flutter run -d chrome
) else (
    echo Exiting...
    goto end
)

echo.
echo ===============================================
echo App is connected to your website!
echo You can control everything from admin panel:
echo https://www.zainalabidin.pro/admin
echo ===============================================

:end
pause
