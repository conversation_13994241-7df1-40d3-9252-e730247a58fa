{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981bd0d34d65ea2198f5bf0b7b92add2c5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98398bac938fb47b84c16f19633c15c92f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9848785ac6a56c3a0eb07eee315920f6b2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839d79a52f24f54321de12379511903ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9848785ac6a56c3a0eb07eee315920f6b2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894fef8acb7de742f96aaab49fd64da62", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987aa25cc4246704783212fddf6332773a", "guid": "bfdfe7dc352907fc980b868725387e982ef6da7a3a60632d8f8022694bd66dd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf0dd891c6d0c69fe5d66792366f8dce", "guid": "bfdfe7dc352907fc980b868725387e98d5b97975f2350a08cf53ffeffcac67ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ae6846c5b9c06e5ab409f8a9f8a4c1a", "guid": "bfdfe7dc352907fc980b868725387e982a831a1daaca5b26d69021efadf726c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d074ba8474c3003e7974dd427838dc2d", "guid": "bfdfe7dc352907fc980b868725387e9893021a663ec0153aef3852a58d009c9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816f8869aaa8fe956da17dec93d1338da", "guid": "bfdfe7dc352907fc980b868725387e986796d36a2bdd3edb6b59e7129fd5a2bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831ba90cbca557b049f3e395fa5ac0235", "guid": "bfdfe7dc352907fc980b868725387e982e3cc8a1179bcd8b86b68167afee1e90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876077045676e8e083c5634768a59aaff", "guid": "bfdfe7dc352907fc980b868725387e98c0be046fcf8cb428c9d30ae21d942726", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852075207b3aa714bdf00d4c454178982", "guid": "bfdfe7dc352907fc980b868725387e9823853b970b232b2ee98e8e4fc95cac83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b9fb35baea4a8c967e97977fab98e0b", "guid": "bfdfe7dc352907fc980b868725387e98f79b7fe35423f642e91efea7931e4532", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca9d2e959017055f4ba5a3e8d3d41244", "guid": "bfdfe7dc352907fc980b868725387e984f6b3b7d4f466465da9e74bca70c619a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f10f66c544ca88d25a5cea3195f8134", "guid": "bfdfe7dc352907fc980b868725387e98a21439c050c6574e9eda2a7c51d789c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981849aaddc3b5f997a6087c082f5b5200", "guid": "bfdfe7dc352907fc980b868725387e987ef5d73bb1bcb9d251b3abc88056c8c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892e03eaa642e31dad236a7261f6e5274", "guid": "bfdfe7dc352907fc980b868725387e9834bceb04a1a4262410f8691a86e50ac0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bc5eb4b3e2df9cdc3b75af11ba19541", "guid": "bfdfe7dc352907fc980b868725387e9819a72032caea2c8a2a1d4d8a68690ede", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cb670c30834c822a6c0316e9c6c44e4", "guid": "bfdfe7dc352907fc980b868725387e989314e081c3f5bfab1279e3c2c1139a24", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983145ad27ab9a5d703e29269b9b2b583a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980d74fa175bc1051d40dcf446931222c1", "guid": "bfdfe7dc352907fc980b868725387e98446126075935aea39a9e1063fe80734a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98284b76ebad863800262dc1e1a1184462", "guid": "bfdfe7dc352907fc980b868725387e9852b28a641c87eaad7787397417bf81c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ec400626b5243240bc5d8bc7b31294b", "guid": "bfdfe7dc352907fc980b868725387e9850004e812d7475a48e09f9d39d1fcd0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f78caa99d281d1c9d51c153378c6e5c6", "guid": "bfdfe7dc352907fc980b868725387e9849968048093435d2a565774782e9f29d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e4f4414e5e258049fe47f226db019d0", "guid": "bfdfe7dc352907fc980b868725387e981550500afbebd6a16545417108df5269"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a47c57440ba4c21203a06ee6a13708a", "guid": "bfdfe7dc352907fc980b868725387e989a62207c5988db84ddcac0078af53d6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98688bfc87aa1f7ad22729a2c480ac6fab", "guid": "bfdfe7dc352907fc980b868725387e98d0e36eae7c250ccd059d9dcc1ce416eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5d6ac1659fef82240781a1478c59229", "guid": "bfdfe7dc352907fc980b868725387e98aaf08e1539082f8b542f01b83c7e8d18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2126bd6937de9841431f24c575481e0", "guid": "bfdfe7dc352907fc980b868725387e98c04b1033e4e3e2b45fd298d396b5d875"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980045d6827fee439e7ac6d844fde65136", "guid": "bfdfe7dc352907fc980b868725387e98b2e8dea6cd344be14bd1567b9270e89b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881f8672784613cc4f5b9671c0199bad2", "guid": "bfdfe7dc352907fc980b868725387e98a89106363ecf1c82c77a5e97d941ae89"}], "guid": "bfdfe7dc352907fc980b868725387e9884539da3bdf2ed4ad426ca202aaf6bc7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e98c1dae7e29df157eddda815ae5462108c"}], "guid": "bfdfe7dc352907fc980b868725387e98119e723bd2257d68f10fcea70feb1342", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981c10b7a3ff26395a834064a0172d6124", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98a3435bfc3b1be550d78f0653eca9c848", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}