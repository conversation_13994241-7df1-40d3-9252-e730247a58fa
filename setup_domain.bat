@echo off
chcp 65001 >nul
echo ===============================================
echo        إعداد التطبيق للموقع
echo        Domain Setup Script
echo ===============================================
echo.

echo التحقق من متطلبات النظام...
echo.

REM التحقق من وجود PHP
php -v >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP غير مثبت على النظام
    echo يرجى تثبيت PHP أولاً
    pause
    exit /b 1
)
echo ✅ PHP موجود

REM التحقق من وجود Composer
composer --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Composer غير مثبت على النظام
    echo يرجى تثبيت Composer أولاً
    pause
    exit /b 1
)
echo ✅ Composer موجود

echo.
echo إعداد ملف البيئة...

REM إنشاء ملف .env إذا لم يكن موجوداً
if not exist .env (
    copy .env.example .env >nul
    echo ✅ تم إنشاء ملف .env
) else (
    echo ⚠️ ملف .env موجود بالفعل
)

echo.
echo يرجى إدخال معلومات الدومين:
set /p domain_url="أدخل اسم الدومين (مثال: https://yourdomain.com): "
set /p db_name="أدخل اسم قاعدة البيانات: "
set /p db_user="أدخل اسم مستخدم قاعدة البيانات: "
set /p db_password="أدخل كلمة مرور قاعدة البيانات: "

echo.
echo تحديث إعدادات التطبيق...

REM تحديث ملف .env باستخدام PowerShell
powershell -Command "(Get-Content .env) -replace 'APP_URL=.*', 'APP_URL=%domain_url%' | Set-Content .env"
powershell -Command "(Get-Content .env) -replace 'DB_DATABASE=.*', 'DB_DATABASE=%db_name%' | Set-Content .env"
powershell -Command "(Get-Content .env) -replace 'DB_USERNAME=.*', 'DB_USERNAME=%db_user%' | Set-Content .env"
powershell -Command "(Get-Content .env) -replace 'DB_PASSWORD=.*', 'DB_PASSWORD=%db_password%' | Set-Content .env"
powershell -Command "(Get-Content .env) -replace 'APP_ENV=.*', 'APP_ENV=production' | Set-Content .env"
powershell -Command "(Get-Content .env) -replace 'APP_DEBUG=.*', 'APP_DEBUG=false' | Set-Content .env"

echo ✅ تم تحديث ملف .env

echo.
echo تثبيت تبعيات التطبيق...
composer install --optimize-autoloader --no-dev
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت التبعيات
    pause
    exit /b 1
)
echo ✅ تم تثبيت التبعيات

echo.
echo إنشاء مفتاح التطبيق...
php artisan key:generate --force
echo ✅ تم إنشاء مفتاح التطبيق

echo.
echo إنشاء رابط التخزين...
php artisan storage:link
echo ✅ تم إنشاء رابط التخزين

echo.
echo تحسين أداء التطبيق...
php artisan config:cache
php artisan route:cache
php artisan view:cache
echo ✅ تم تحسين الأداء

echo.
echo اختبار الاتصال بقاعدة البيانات...
php artisan tinker --execute="try { DB::connection()->getPdo(); echo 'اتصال ناجح'; } catch(Exception $e) { echo 'فشل الاتصال: ' . $e->getMessage(); }"

echo.
echo إنشاء ملف .htaccess...
if not exist .htaccess (
    (
        echo ^<IfModule mod_rewrite.c^>
        echo     RewriteEngine On
        echo     RewriteRule ^^(.*)$ public/$1 [L]
        echo ^</IfModule^>
    ) > .htaccess
    echo ✅ تم إنشاء ملف .htaccess
) else (
    echo ⚠️ ملف .htaccess موجود بالفعل
)

echo.
echo ===============================================
echo ✅ تم إعداد التطبيق بنجاح!
echo.
echo الخطوات التالية:
echo 1. تأكد من إعداد DNS للدومين
echo 2. قم بتشغيل: php artisan migrate
echo 3. اختبر الموقع على: %domain_url%
echo 4. قم بإعداد SSL Certificate للأمان
echo.
echo ⚠️ لا تنس تشغيل arabic_language_setup.sql لتفعيل اللغة العربية
echo ===============================================
echo.
pause
