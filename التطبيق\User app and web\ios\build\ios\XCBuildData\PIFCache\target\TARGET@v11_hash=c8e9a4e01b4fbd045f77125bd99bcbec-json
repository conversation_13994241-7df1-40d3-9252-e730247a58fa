{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981b378441e86cfc96643d133fe976c315", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b842cb635d56cdf00c7a9217474940b4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98561decf083670b33708026e22fa8fbd5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989061711c25df60cd7a42c892f80f97c3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98561decf083670b33708026e22fa8fbd5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98176a99453b8e50ff021994b275907e11", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9836a52e8ace2002cc87637a4a48123e94", "guid": "bfdfe7dc352907fc980b868725387e98f1a5ce7c6276ff4f5af4c8010139d969", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a7f996db0723c9901e699f063d3e7a9", "guid": "bfdfe7dc352907fc980b868725387e98ba91e089026701e0982faf39fc50056d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834139336eb28bf0433a7c2bd1a2fa918", "guid": "bfdfe7dc352907fc980b868725387e98b29c37ef6fbd6454bcba86aee7959896", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986d656dca04da8b6499e0364830358df7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9861b07ca2552e759abd1a62dd0fdf6b79", "guid": "bfdfe7dc352907fc980b868725387e98cae1551eeaae71a07f9bd7bc92c27348"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fd36bbe186d5860a45a92e7bafeb82c", "guid": "bfdfe7dc352907fc980b868725387e98a4076a36fb7b6f220e61d7aa83dcc821"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5885d0f73690caa50a35f975232cb4d", "guid": "bfdfe7dc352907fc980b868725387e98d91af963a744c6262c0f9cc65425ee9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98532d1b09df5adc67915591a3a05ec24e", "guid": "bfdfe7dc352907fc980b868725387e983f34e0578c51da48a8f1c6ab5a2474b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd058b6c0ee6ceec92d92de3006036b5", "guid": "bfdfe7dc352907fc980b868725387e986accedb9976457c744a8f67633dc86c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eff96cf9d1ce12fa447fc103d473ce5a", "guid": "bfdfe7dc352907fc980b868725387e987cb4fc3c4ec0c26df541ce2edf474193"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987768e48a6e02095661f0b567e576a5e9", "guid": "bfdfe7dc352907fc980b868725387e98038cc984045f79a69983769f6cf2a632"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfe0eb4e4292e01c2983b604bb186626", "guid": "bfdfe7dc352907fc980b868725387e98e31e44ad1e6e629d720b4854d15a0bac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825e988da6b0ceaf2dae87a3ea7191509", "guid": "bfdfe7dc352907fc980b868725387e98081299d0fee81d3c1ebdf0f314141e41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b67c254c86dbad0d4627721f80a5b2d5", "guid": "bfdfe7dc352907fc980b868725387e988a9ae80dd12ddd2754a5b2846d27e94f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98118f54436096dc67c944627650171f71", "guid": "bfdfe7dc352907fc980b868725387e9867e69b00d2bc76f49e2412467f386a2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef12683a2f59b88913d5e2b89165b0ad", "guid": "bfdfe7dc352907fc980b868725387e98172cf97a3406ff4ed6d2f6b25c176d33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af3ac265bfce4d54f951322984404f2b", "guid": "bfdfe7dc352907fc980b868725387e98385f6d1014bced319c4d0140844ebdc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe5e8108bee92c683c1c1c5727ce539c", "guid": "bfdfe7dc352907fc980b868725387e987b410ff2caeebd3a8de1852851946529"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a99c3c950db195f46fd345b5d8aea062", "guid": "bfdfe7dc352907fc980b868725387e989f5c050b79592ebfd253206e36b9abe5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a07c3d8b908b378c6ec2c868d9cf680", "guid": "bfdfe7dc352907fc980b868725387e98b4d564a413cffc933e7fc3d75477103d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985df247f75bbb5eb755bbba547cf7874d", "guid": "bfdfe7dc352907fc980b868725387e9878b1c85f2da3b080e1168868631802ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e680cfa39e96f485e7b21d97cd5f4d29", "guid": "bfdfe7dc352907fc980b868725387e98f32b0d1c0a5c27c559607508fe995fc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98197b9d298b29acc19deb4a56cb212ff6", "guid": "bfdfe7dc352907fc980b868725387e98ec0a6dab7e42f58e268c901f8f08aa96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8b44bf3705e08c0ac74c32d8c537c68", "guid": "bfdfe7dc352907fc980b868725387e9891258bea2ac09c06841c86891c802825"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ed2142e7a493f7557c5067cedf72fb6", "guid": "bfdfe7dc352907fc980b868725387e984db661f3d26725220076ccc9533848e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fc2636d410b4dfe37d749970fcd4d53", "guid": "bfdfe7dc352907fc980b868725387e982980c0fd68748310af4a7037d389bbce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98465a6c82d9bc1abe348fd614a74c3b21", "guid": "bfdfe7dc352907fc980b868725387e983a003cd2dc4d66c8b0507689b5b756ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986493ad0df43364eeb4e3e17b4428c545", "guid": "bfdfe7dc352907fc980b868725387e981c8548ef82717746ae26a4f22107e663"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98766ca73ef1c506a426e7da8b12140417", "guid": "bfdfe7dc352907fc980b868725387e98dd998437b4a6035a3ad6d6a3f32090cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ba2658b97cf2f76ee30c69326bf057c", "guid": "bfdfe7dc352907fc980b868725387e980b71e2a740a55b6c5eb925b421da6b48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eba94184044e3297e1d3b7129c4c414", "guid": "bfdfe7dc352907fc980b868725387e985e0ec9008e503e0e164d9833c2a05ba4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bb44b94dc5ff5419ae5b9c201d2b502", "guid": "bfdfe7dc352907fc980b868725387e987c0273605b525ae5a65aa912c20572d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98783ebccb42eb3aaaba36613fdb4a4d47", "guid": "bfdfe7dc352907fc980b868725387e98147bfe6e5bff4a23a55eff4283586658"}], "guid": "bfdfe7dc352907fc980b868725387e983c55e23e3a3bd639db8913664df0f50a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c7bf7ee5fbc558c481104fe48eb4be0", "guid": "bfdfe7dc352907fc980b868725387e9858e70e6cc57524f2b0be31486484be10"}], "guid": "bfdfe7dc352907fc980b868725387e98136b334b071cfe79c2d7dfcfe577184a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98207df5236f8b8c9d5716ab1bb6341597", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e98424a0579f05b8aa7b116a0e1ae14c72d", "name": "FirebaseSessions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a41ba860aa6fc56673ac239987133d67", "name": "FirebaseSessions.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}