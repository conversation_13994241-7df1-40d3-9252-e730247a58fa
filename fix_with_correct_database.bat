@echo off
chcp 65001 >nul
echo ===============================================
echo        إصلاح الموقع بقاعدة البيانات الصحيحة
echo       Fix Website with Correct Database
echo ===============================================
echo.

echo 🔧 استخدام قاعدة البيانات: kidzrcle_kalfa
echo 👤 اسم المستخدم: kidzrcle_kalfa
echo 🔑 كلمة المرور: kidzrcle_kalfa
echo.

echo 1️⃣ تنظيف جميع أنواع الكاش...
php artisan cache:clear
echo ✅ تم تنظيف cache

php artisan config:clear
echo ✅ تم تنظيف config

php artisan route:clear
echo ✅ تم تنظيف routes

php artisan view:clear
echo ✅ تم تنظيف views

php artisan optimize:clear
echo ✅ تم التنظيف الشامل

echo.
echo 2️⃣ إنشاء مفتاح تطبيق جديد...
php artisan key:generate --force
echo ✅ تم إنشاء مفتاح جديد

echo.
echo 3️⃣ اختبار الاتصال بقاعدة البيانات...
php artisan tinker --execute="try { DB::connection()->getPdo(); echo 'اتصال ناجح بقاعدة البيانات!'; } catch(Exception $e) { echo 'خطأ: ' . $e->getMessage(); }"

echo.
echo 4️⃣ فحص الجداول الموجودة...
php artisan tinker --execute="try { echo 'عدد الجداول: ' . count(DB::select('SHOW TABLES')); } catch(Exception $e) { echo 'لا توجد جداول - سنقوم بإنشائها'; }"

echo.
echo 5️⃣ تشغيل migrations إذا لزم الأمر...
php artisan migrate --force
echo ✅ تم تشغيل migrations

echo.
echo 6️⃣ إنشاء رابط التخزين...
php artisan storage:link
echo ✅ تم إنشاء رابط التخزين

echo.
echo 7️⃣ تشغيل seeders الأساسية...
php artisan db:seed --class=AdminTableSeeder --force
php artisan db:seed --class=BusinessSettingsTableSeeder --force
echo ✅ تم تشغيل seeders

echo.
echo 8️⃣ تحسين الأداء...
php artisan config:cache
php artisan route:cache
echo ✅ تم تحسين الأداء

echo.
echo 9️⃣ اختبار نهائي...
php artisan tinker --execute="try { echo 'الموقع جاهز! عدد إعدادات العمل: ' . DB::table('business_settings')->count(); } catch(Exception $e) { echo 'تحقق من الجداول'; }"

echo.
echo ===============================================
echo 🎉 تم الانتهاء من الإصلاح!
echo.
echo ✅ قاعدة البيانات: kidzrcle_kalfa
echo ✅ الاتصال: تم بنجاح
echo ✅ الموقع: جاهز للعمل
echo.
echo 🌐 جرب الموقع الآن:
echo https://www.zainalabidin.pro
echo.
echo 🔐 للدخول إلى لوحة التحكم:
echo https://www.zainalabidin.pro/admin
echo.
echo ===============================================
pause
