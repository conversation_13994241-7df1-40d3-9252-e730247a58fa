@echo off
chcp 65001 >nul
echo ===============================================
echo        إصلاح مشكلة قاعدة البيانات
echo       Fix Database Connection
echo ===============================================
echo.

echo 🔧 إصلاح مشكلة الاتصال بقاعدة البيانات...
echo.

echo 1️⃣ تنظيف الكاش...
php artisan cache:clear
php artisan config:clear

echo.
echo 2️⃣ إنشاء مفتاح تطبيق جديد...
php artisan key:generate --force

echo.
echo 3️⃣ اختبار الاتصال بقاعدة البيانات...
php artisan tinker --execute="try { DB::connection()->getPdo(); echo 'اتصال ناجح!'; } catch(Exception $e) { echo 'خطأ: ' . $e->getMessage(); }"

echo.
echo ===============================================
echo 📋 معلومات قاعدة البيانات المطلوبة:
echo.
echo اسم قاعدة البيانات: zainalabidin_db
echo اسم المستخدم: zainalabidin_user  
echo كلمة المرور: ZainAbidin2024!
echo.
echo ===============================================
echo 🚀 الخطوات التالية:
echo.
echo 1. إنشاء قاعدة البيانات في cPanel أو phpMyAdmin
echo 2. استخدم المعلومات أعلاه
echo 3. أو شغل ملف setup_database.sql في phpMyAdmin
echo 4. ثم شغل: php artisan migrate
echo.
echo إذا كان لديك معلومات قاعدة بيانات مختلفة،
echo عدل ملف .env بالمعلومات الصحيحة
echo.
echo ===============================================
pause
