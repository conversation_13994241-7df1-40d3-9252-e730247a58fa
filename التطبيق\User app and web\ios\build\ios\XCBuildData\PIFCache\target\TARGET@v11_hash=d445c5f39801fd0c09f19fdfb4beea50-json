{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dab8eec0a3f4fc9cde3349df144d0434", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eba51422b78a2483f2440c04ad139244", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eba51422b78a2483f2440c04ad139244", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ad9db89186636ca6e573d1d157dd311b", "guid": "bfdfe7dc352907fc980b868725387e98fe92b5afa8fe58bae3c9229d058e616a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e6cb0d2a85fee073ef18c571d3148e0", "guid": "bfdfe7dc352907fc980b868725387e985965519b3ec2b9053365fa530bf643bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddf43ef1b42d0801c6624537138aa795", "guid": "bfdfe7dc352907fc980b868725387e98fa9d97f54a76579d59e404ebe634bcdc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9f8271d0eae5070ab6d7ccca82e6971", "guid": "bfdfe7dc352907fc980b868725387e9850bdaa7011aa3c91740c213b1a7fa2cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98130a3c00f7b5d43f8dc9761b8aef98b8", "guid": "bfdfe7dc352907fc980b868725387e98b69297aee86961e47e29414c45e849ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804511260477e3be8bd5ae8725bca5e82", "guid": "bfdfe7dc352907fc980b868725387e985ca3c30a7f4851eb9b4ef33952a3f482", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98913d6e3d1a41c6bb0f7e48f16e8f7cfb", "guid": "bfdfe7dc352907fc980b868725387e9812df9d9973c9f97679f7de972b48f817", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881e6b4239d65a71df73b139a0d3b3566", "guid": "bfdfe7dc352907fc980b868725387e98abf8d1a3dd0a4ead2fb6398cea2425ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce400789d57093b025b438696c020157", "guid": "bfdfe7dc352907fc980b868725387e986340d5835ff3d4930a87aff596993c8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829218a7b7e3488878aafea897dfc25dd", "guid": "bfdfe7dc352907fc980b868725387e98282e717420c34bd36656734c015ab0db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98124f0cdfd6d36927af6ef0d108326fe7", "guid": "bfdfe7dc352907fc980b868725387e98f79617ce3eea92130c3e8deb24ac6052", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf00eb5aa96aa5922e6970ff7fc43ea4", "guid": "bfdfe7dc352907fc980b868725387e989a9c98af54d171ffc5dcfcd328a9f656", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bab0b65d0ea2ce3e4862e4f20b44e00", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859ee709183664b166e321cd9a6c5413e", "guid": "bfdfe7dc352907fc980b868725387e98892ab7216e572450585dbb7fe836780a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825b7ac92683b195982fa8f7a6f4a3de4", "guid": "bfdfe7dc352907fc980b868725387e983d12f44f6ad546c0e51ad40a38a62758", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f6287a05b49d8f7d893118620d7113e", "guid": "bfdfe7dc352907fc980b868725387e98a42139f6edf79b3b7cbcf3d1471e4431", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833155686f6711f5282d49419cdcf998d", "guid": "bfdfe7dc352907fc980b868725387e98d954eaec4de1c4988534c19c3b497a42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a08e38421848c9888c65665b62080bc3", "guid": "bfdfe7dc352907fc980b868725387e98a1ebbf17f73356fde3a4c9c9ccaaf541", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1f753bba20dc78e75551af9d70c0a14", "guid": "bfdfe7dc352907fc980b868725387e9821e54dc62d1296156df602834f20fcc9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895644be5e7284e4313235d90604fefce", "guid": "bfdfe7dc352907fc980b868725387e986061ae92e4bce152885cc5c3a1c0e4d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb2f13b3c3157a3040f67c16a2d2db99", "guid": "bfdfe7dc352907fc980b868725387e98e7e268d654b1604ef998bf4cc75336d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98899189eda18971a6517277282de1134d", "guid": "bfdfe7dc352907fc980b868725387e9854f174ef0b05263e6dcd47023ed0496c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd80c168e74a13315601c36d37fb8c6b", "guid": "bfdfe7dc352907fc980b868725387e98a3f25623979afa23466422cd7a073ec5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98df787e905957789a0fbfb237f8a8bbfd", "guid": "bfdfe7dc352907fc980b868725387e98ffd488b77237b8fc440d41413454bb07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbd102ffcaf429bf3d6a5ac87275668a", "guid": "bfdfe7dc352907fc980b868725387e98867e367d9e5822a1ed3da2e5d879f4ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a3850c792ead56b67b818f790a6b772", "guid": "bfdfe7dc352907fc980b868725387e98f4e8406eff8c37b5cf57678723c2865d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d82edaca383bd641cd831c38d9fc94d", "guid": "bfdfe7dc352907fc980b868725387e986a9a5442fb214160e10f186b31f4351e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9fbb9a0ebe6e89d6044a2c9812db108", "guid": "bfdfe7dc352907fc980b868725387e9856735a0df7a10f65de67a62396d1be8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bbc51ecfc8fe0b7345019c61bad97f2", "guid": "bfdfe7dc352907fc980b868725387e98d490619ce99b46bd5cde304795058c5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98925dad3b3ab389e4a140849ab87a4e83", "guid": "bfdfe7dc352907fc980b868725387e985b6b972d31abdb37d83e0217390f1111"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98732625dd3563489e21e53840df1be2b4", "guid": "bfdfe7dc352907fc980b868725387e9826e5537c184a98a817fabddd8b1af46b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c0eb726b589eb7e592dfa6ce92d7e27", "guid": "bfdfe7dc352907fc980b868725387e98a6f139e04c42be401173e09b8eb64304"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f6a457730efd918d6b9b6ee916fd24f", "guid": "bfdfe7dc352907fc980b868725387e98bba9b3f581dbe27be7f77e7dba39135c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a7f343365b04aa37d0daa233874a2c2", "guid": "bfdfe7dc352907fc980b868725387e98f5a18a02b63f8074b1935286ce097e7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b079df9d49284b28ce6fc5ba6da84bfe", "guid": "bfdfe7dc352907fc980b868725387e982a4d902c47d2e168ad1b9faadb826b40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0c4ed72ae5c59199ff5ef5d1f9f4eee", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e8453208637c337908210ffb919d566", "guid": "bfdfe7dc352907fc980b868725387e98c7209acb4bbc11473400b95ba264e570"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987791333c8c971b9c8da502bc6f0d814f", "guid": "bfdfe7dc352907fc980b868725387e98b9ad21760386409e6a8418d260c875a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a21118393d0cb36afa375506280e8f34", "guid": "bfdfe7dc352907fc980b868725387e9869daee10844bdc1377101bb06b07af6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860f7677a4a55d316f784c466d709fa55", "guid": "bfdfe7dc352907fc980b868725387e9880b83d2e94cb821fc34990ec5eb08f30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9510cca09628c20d4fb35bbaee5ec4c", "guid": "bfdfe7dc352907fc980b868725387e983fc554d229dec51832d227dc3c6cd4f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828ffc08f191f7528dbbdd65fad31dc8a", "guid": "bfdfe7dc352907fc980b868725387e984d92ce2a6a775539ad9cd12b7b3c8702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dee929e3f75a23d134b24ae449793c04", "guid": "bfdfe7dc352907fc980b868725387e98b55b1eb31da0207c73caefaa35380afd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f35fba3b2e0a48fe90d500ac4e2c4eb1", "guid": "bfdfe7dc352907fc980b868725387e982a5ad3c71031968c83cf1f0dd2275285"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}