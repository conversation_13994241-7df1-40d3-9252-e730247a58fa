# دليل تحويل النظام إلى اللغة العربية
# Arabic Language Setup Guide

## الملفات المطلوبة:

### 1. ملف قاعدة البيانات
**الملف:** `arabic_language_setup.sql`
**الوصف:** يحتوي على استعلامات SQL لتحويل النظام إلى اللغة العربية

### 2. ملف فحص الحالة
**الملف:** `check_arabic_status.sql`
**الوصف:** للتحقق من حالة اللغة العربية في النظام

### 3. ملفات مسح الكاش
**الملفات:**
- `clear_cache_arabic.bat` (لنظام Windows)
- `clear_cache_arabic.sh` (لأنظمة Linux/Mac)
**الوصف:** لتنظيف الكاش تلقائياً بعد تطبيق التغييرات

## خطوات التطبيق:

### الخطوة 1: استيراد ملف SQL
```sql
-- قم بتشغيل هذا الأمر في قاعدة البيانات
SOURCE arabic_language_setup.sql;
```

أو استخدم phpMyAdmin:
1. افتح phpMyAdmin
2. اختر قاعدة البيانات الخاصة بالمشروع
3. اذهب إلى تبويب "Import"
4. اختر ملف `arabic_language_setup.sql`
5. اضغط "Go"

### الخطوة 2: مسح الكاش
قم بتشغيل هذه الأوامر في terminal:
```bash
php artisan cache:clear
php artisan config:clear
php artisan view:clear
php artisan route:clear
```

### الخطوة 3: التحقق من ملفات اللغة
تأكد من وجود الملفات التالية:
- `resources/lang/ar/lang.php` ✅ (موجود)
- `resources/lang/ar/new-lang.php` ✅ (موجود)

## ما يحدث عند تطبيق الملف:

### 1. تحديث إعدادات اللغة:
- يتم تعيين العربية كلغة افتراضية
- يتم تفعيل اتجاه RTL للعربية
- يتم الاحتفاظ بالإنجليزية كلغة ثانوية

### 2. تحديث بيانات المستخدمين:
- تحويل لغة جميع المستخدمين إلى العربية
- تحويل لغة الضيوف إلى العربية

### 3. تحديث إعدادات العرض:
- تعديل موضع رمز العملة ليناسب العربية
- تفعيل الاتجاه من اليمين إلى اليسار (RTL)

## الجداول المتأثرة:

1. **business_settings**
   - تحديث `system_language`
   - تحديث إعدادات العملة

2. **users**
   - تحديث `current_language_key` إلى 'ar'

3. **guests**
   - تحديث `current_language_key` إلى 'ar'

## التحقق من نجاح التطبيق:

### 1. في لوحة الإدارة:
- اذهب إلى Settings > Language Setup
- تأكد من أن العربية مفعلة وهي الافتراضية

### 2. في الواجهة الأمامية:
- تحقق من ظهور النصوص بالعربية
- تأكد من اتجاه RTL

### 3. في قاعدة البيانات:
```sql
-- تحقق من إعدادات اللغة
SELECT * FROM business_settings WHERE key_name = 'system_language';

-- تحقق من لغة المستخدمين
SELECT current_language_key, COUNT(*) FROM users GROUP BY current_language_key;
```

## استكشاف الأخطاء:

### إذا لم تظهر العربية:
1. تأكد من تشغيل أوامر مسح الكاش
2. تحديث الصفحة في المتصفح
3. تحقق من وجود ملفات اللغة العربية

### إذا كان الاتجاه خاطئ:
1. تأكد من أن CSS يدعم RTL
2. تحقق من إعدادات direction في قاعدة البيانات

### إذا ظهرت أخطاء في قاعدة البيانات:
1. تأكد من أن الجداول موجودة
2. تحقق من صلاحيات قاعدة البيانات
3. راجع رسائل الخطأ في logs

## ملاحظات مهمة:

1. **النسخ الاحتياطي**: قم بعمل نسخة احتياطية من قاعدة البيانات قبل التطبيق
2. **البيئة**: يُنصح بالتجربة في بيئة التطوير أولاً
3. **الترجمات**: قد تحتاج لتحديث بعض الترجمات يدوياً
4. **CSS**: تأكد من أن التصميم يدعم RTL بشكل صحيح

## أوامر مفيدة:

```bash
# مسح جميع أنواع الكاش
php artisan optimize:clear

# إعادة تحميل الإعدادات
php artisan config:cache

# التحقق من حالة النظام
php artisan about
```

تم إنشاء هذا الدليل لمساعدتك في تحويل النظام إلى اللغة العربية بسهولة وأمان.
