@echo off
chcp 65001 >nul
echo ===============================================
echo        تثبيت Flutter تلقائياً
echo       Auto Install Flutter
echo ===============================================
echo.

echo 📱 تثبيت Flutter تلقائياً...
echo.

echo 1️⃣ فحص Git...
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Git غير مثبت
    echo 📥 حمل Git من: https://git-scm.com/download/win
    echo ثم أعد تشغيل هذا الملف
    pause
    exit /b 1
)

echo ✅ Git موجود
echo.

echo 2️⃣ إنشاء مجلد Flutter...
if exist "C:\flutter" (
    echo ⚠️ مجلد Flutter موجود، سيتم حذفه وإعادة التثبيت
    rmdir /s /q "C:\flutter"
)

echo.
echo 3️⃣ تحميل Flutter من GitHub...
echo هذا قد يستغرق بضع دقائق...
git clone https://github.com/flutter/flutter.git -b stable C:\flutter

if %errorlevel% neq 0 (
    echo ❌ فشل في تحميل Flutter
    echo جرب التثبيت اليدوي
    pause
    exit /b 1
)

echo ✅ تم تحميل Flutter
echo.

echo 4️⃣ إضافة Flutter إلى PATH...
setx PATH "%PATH%;C:\flutter\bin" /M >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ تم إضافة Flutter إلى PATH
) else (
    echo ⚠️ فشل في إضافة PATH تلقائياً
    echo يرجى إضافة C:\flutter\bin إلى PATH يدوياً
)

echo.
echo 5️⃣ تحديث PATH للجلسة الحالية...
set "PATH=%PATH%;C:\flutter\bin"

echo.
echo 6️⃣ اختبار Flutter...
C:\flutter\bin\flutter.bat --version
if %errorlevel% equ 0 (
    echo ✅ Flutter يعمل بشكل صحيح!
) else (
    echo ❌ مشكلة في Flutter
)

echo.
echo 7️⃣ تشغيل Flutter Doctor...
C:\flutter\bin\flutter.bat doctor

echo.
echo ===============================================
echo ✅ تم تثبيت Flutter!
echo.
echo 🚀 الآن يمكنك بناء التطبيق:
echo 1. أعد تشغيل Command Prompt
echo 2. شغل: build_apk.bat
echo.
echo أو اضغط Y للمتابعة مباشرة:
echo ===============================================

set /p continue="هل تريد بناء التطبيق الآن؟ (Y/N): "
if /i "%continue%"=="y" (
    echo.
    echo 🚀 بناء التطبيق...
    cd "User app and web"
    C:\flutter\bin\flutter.bat clean
    C:\flutter\bin\flutter.bat pub get
    C:\flutter\bin\flutter.bat build apk --release
    
    if exist "build\app\outputs\flutter-apk\app-release.apk" (
        echo.
        echo ✅ تم بناء التطبيق بنجاح!
        echo 📁 الملف في: build\app\outputs\flutter-apk\app-release.apk
        explorer "build\app\outputs\flutter-apk"
    ) else (
        echo ❌ فشل في بناء التطبيق
    )
)

pause
