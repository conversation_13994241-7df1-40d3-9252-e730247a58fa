<?php
/**
 * ملف فحص الأخطاء وإصلاحها
 * Error Check and Fix Script
 */

echo "===============================================\n";
echo "        فحص أخطاء الموقع\n";
echo "        Website Error Check\n";
echo "===============================================\n\n";

// ألوان للنص
class Colors {
    public static $GREEN = "\033[0;32m";
    public static $RED = "\033[0;31m";
    public static $YELLOW = "\033[1;33m";
    public static $BLUE = "\033[0;34m";
    public static $NC = "\033[0m";
}

function printStatus($message) {
    echo Colors::$GREEN . "✅ " . $message . Colors::$NC . "\n";
}

function printError($message) {
    echo Colors::$RED . "❌ " . $message . Colors::$NC . "\n";
}

function printWarning($message) {
    echo Colors::$YELLOW . "⚠️  " . $message . Colors::$NC . "\n";
}

function printInfo($message) {
    echo Colors::$BLUE . "ℹ️  " . $message . Colors::$NC . "\n";
}

// فحص ملف .env
printInfo("فحص ملف .env...");
if (file_exists('.env')) {
    printStatus("ملف .env موجود");
    
    // قراءة المتغيرات المهمة
    $envContent = file_get_contents('.env');
    
    if (strpos($envContent, 'APP_KEY=') !== false) {
        printStatus("APP_KEY موجود");
    } else {
        printError("APP_KEY مفقود - شغل: php artisan key:generate");
    }
    
    if (strpos($envContent, 'DB_DATABASE=') !== false) {
        printStatus("إعدادات قاعدة البيانات موجودة");
    } else {
        printError("إعدادات قاعدة البيانات مفقودة");
    }
} else {
    printError("ملف .env غير موجود - انسخ من .env.example");
}

echo "\n";

// فحص صلاحيات المجلدات
printInfo("فحص صلاحيات المجلدات...");

$directories = ['storage', 'bootstrap/cache'];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            printStatus("مجلد $dir قابل للكتابة");
        } else {
            printError("مجلد $dir غير قابل للكتابة");
        }
    } else {
        printError("مجلد $dir غير موجود");
    }
}

echo "\n";

// فحص ملفات مهمة
printInfo("فحص الملفات المهمة...");

$files = [
    'vendor/autoload.php' => 'Composer autoload',
    'bootstrap/app.php' => 'Bootstrap file',
    'public/index.php' => 'Entry point',
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        printStatus("$description موجود");
    } else {
        printError("$description مفقود: $file");
    }
}

echo "\n";

// فحص الاتصال بقاعدة البيانات
printInfo("فحص قاعدة البيانات...");
try {
    // تحميل Laravel
    if (file_exists('vendor/autoload.php')) {
        require_once 'vendor/autoload.php';
        
        if (file_exists('bootstrap/app.php')) {
            $app = require_once 'bootstrap/app.php';
            $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
            $kernel->bootstrap();
            
            // اختبار الاتصال
            $pdo = DB::connection()->getPdo();
            printStatus("قاعدة البيانات متصلة");
        } else {
            printError("ملف bootstrap/app.php مفقود");
        }
    } else {
        printError("Composer autoload مفقود - شغل: composer install");
    }
} catch (Exception $e) {
    printError("خطأ في قاعدة البيانات: " . $e->getMessage());
}

echo "\n";

// فحص آخر الأخطاء في logs
printInfo("فحص آخر الأخطاء...");
$logFile = 'storage/logs/laravel.log';
if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    $lines = explode("\n", $logContent);
    $lastLines = array_slice($lines, -10);
    
    printStatus("آخر 10 أسطر من log:");
    foreach ($lastLines as $line) {
        if (!empty(trim($line))) {
            echo "  " . trim($line) . "\n";
        }
    }
} else {
    printWarning("ملف log غير موجود");
}

echo "\n";

// حلول سريعة
printInfo("حلول سريعة للمشاكل الشائعة:");
echo "1. شغل: composer install\n";
echo "2. شغل: php artisan key:generate\n";
echo "3. شغل: php artisan cache:clear\n";
echo "4. تحقق من إعدادات قاعدة البيانات في .env\n";
echo "5. تأكد من صلاحيات المجلدات\n";

echo "\n===============================================\n";
printInfo("انتهى فحص الأخطاء");
echo "===============================================\n";
?>
