@echo off
chcp 65001 >nul
echo ===============================================
echo        إصلاح خطأ 500 في الموقع
echo         Fix 500 Error
echo ===============================================
echo.

echo 🔧 بدء إصلاح المشاكل...
echo.

echo 1️⃣ تنظيف جميع أنواع الكاش...
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan optimize:clear

echo.
echo 2️⃣ إنشاء مفتاح تطبيق جديد...
php artisan key:generate --force

echo.
echo 3️⃣ إعداد صلاحيات المجلدات...
if exist storage (
    echo تعديل صلاحيات مجلد storage...
    attrib -r storage\* /s /d
)

if exist bootstrap\cache (
    echo تعديل صلاحيات مجلد bootstrap\cache...
    attrib -r bootstrap\cache\* /s /d
)

echo.
echo 4️⃣ إنشاء رابط التخزين...
php artisan storage:link

echo.
echo 5️⃣ فحص قاعدة البيانات...
php artisan migrate:status

echo.
echo 6️⃣ إعادة تحميل الإعدادات...
php artisan config:cache

echo.
echo ===============================================
echo ✅ تم الانتهاء من الإصلاح!
echo.
echo الآن جرب الموقع مرة أخرى:
echo https://www.zainalabidin.pro
echo.
echo إذا استمر الخطأ، تحقق من:
echo 1. إعدادات قاعدة البيانات في .env
echo 2. صلاحيات المجلدات على الخادم
echo 3. ملف logs في storage/logs/laravel.log
echo ===============================================
pause
