<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\RateLimiter;

class ApiHitLimit
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure(Request): (Response|RedirectResponse)  $next
     * @return JsonResponse
     */
    public function handle(Request $request, Closure $next): JsonResponse
    {
        RateLimiter::attempt(
            'create-order:' . auth('api')->id(),
            1,
            function () use ($request, $next) {
                return $next($request);
            }, 10
        );
        return response()->json(TOO_MANY_ATTEMPT_403);
    }
}
