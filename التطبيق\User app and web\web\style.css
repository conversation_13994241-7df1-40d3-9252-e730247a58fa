html,
body {
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;
    background-color: #FFFFFF;
}

body.theme-dark {
    background-color: #111;
}

/* Loader */
.preloader {
  position: fixed;
  inline-size: 100%;
  block-size: 100%;
  background-color: #fff;
  display: grid;
  place-items: center;

}

.theme-dark .preloader {
  background-color: #111;
}

@media (max-width: 1199px) {.header {display: none}}

.preloader-img {
  animation: animloaderimg 3000ms ease-in-out infinite;
}
.preloader-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
}
.preloader .loader {
  width: 140px;
  height: 0.25rem;
  display: inline-block;
  position: relative;
  background: rgba(0, 0, 0, 0.15);
  overflow: hidden;
  border-radius: 50rem;
}
.theme-dark .preloader .loader {
  background: rgba(255, 255, 255, 0.15);
}
.preloader .loader::after {
  content: "";
  width: 30px;
  height: 4.8px;
  background: #000;
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
  animation: animloader 2000ms linear infinite;
  border-radius: 50rem;
}

.header {
  position: fixed;
  inset-block-start: 0;
  inset-inline-start: 0;
  inline-size: 100%;
}
.header-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.28rem 1.6rem;
  background-color: #fff;
  box-shadow: 0px 6px 12px -3px rgba(4, 97, 165, 0.1);
  border-radius: 0 0 0.625rem 0.625rem;
}

.theme-dark .header-container {
  background-color: #111;
}
.header .logo {
  max-height: 30px;

}
.placeholder {
  block-size: 0.825rem;
  inline-size: 3rem;
  background-color: #F5F5F5;
  border-radius: 0.3rem;
}

.theme-dark .placeholder {
  background-color: #111;
}


.placeholder-wide {
  inline-size: 12rem;
}

.d-flex {
  display: flex;
}
.align-items-center {
  align-items: center;
}
.justify-content-center {
  justify-content: center;
}
.justify-content-between {
  justify-content: space-between;
}
.flex-column {
  flex-direction: column;
}
.gap-3 {
  gap: 1rem;
}
.gap-4 {
  gap: 1.5rem;
}
.gap-5 {
  gap: 3rem;
}
@keyframes animloader {
  0% {
    left: 0;
    transform: translateX(-100%);
  }
  100% {
    left: 100%;
    transform: translateX(0%);
  }
}
@keyframes animloaderimg {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-1rem);
  }
  100% {
    transform: translateY(0);
  }
}