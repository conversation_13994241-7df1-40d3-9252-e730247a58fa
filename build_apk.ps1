# PowerShell script to build APK
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host "         Build APK for Mobile App" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host ""

# Check Flutter installation
Write-Host "Step 1: Checking Flutter installation..." -ForegroundColor Yellow
try {
    $flutterVersion = flutter --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Flutter is installed" -ForegroundColor Green
        flutter --version
    } else {
        throw "Flutter not found"
    }
} catch {
    Write-Host "❌ Flutter is not installed!" -ForegroundColor Red
    Write-Host "Please install Flutter first:" -ForegroundColor Yellow
    Write-Host "1. Download from: https://flutter.dev" -ForegroundColor White
    Write-Host "2. Extract to C:\flutter" -ForegroundColor White
    Write-Host "3. Add C:\flutter\bin to PATH" -ForegroundColor White
    Write-Host "4. Restart PowerShell" -ForegroundColor White
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Navigate to app directory
Write-Host "Step 2: Navigating to app directory..." -ForegroundColor Yellow
if (Test-Path "User app and web") {
    Set-Location "User app and web"
    Write-Host "✅ Found app directory" -ForegroundColor Green
} else {
    Write-Host "❌ Cannot find 'User app and web' directory" -ForegroundColor Red
    Write-Host "Make sure you are in the correct folder" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Clean project
Write-Host "Step 3: Cleaning project..." -ForegroundColor Yellow
flutter clean
Write-Host "✅ Project cleaned" -ForegroundColor Green

Write-Host ""

# Get dependencies
Write-Host "Step 4: Getting dependencies..." -ForegroundColor Yellow
flutter pub get
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Dependencies updated" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to get dependencies" -ForegroundColor Red
}

Write-Host ""

# Build APK
Write-Host "Step 5: Building Release APK..." -ForegroundColor Yellow
Write-Host "This may take a few minutes..." -ForegroundColor Cyan
flutter build apk --release

Write-Host ""
Write-Host "===============================================" -ForegroundColor Cyan

# Check if APK was built successfully
$apkPath = "build\app\outputs\flutter-apk\app-release.apk"
if (Test-Path $apkPath) {
    Write-Host "🎉 SUCCESS: APK built successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📁 APK Location:" -ForegroundColor Yellow
    Write-Host $apkPath -ForegroundColor White
    Write-Host ""
    
    # Get file size
    $fileSize = (Get-Item $apkPath).Length / 1MB
    Write-Host "📏 File Size: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "🚀 Next steps:" -ForegroundColor Yellow
    Write-Host "1. Copy APK to your phone" -ForegroundColor White
    Write-Host "2. Enable 'Unknown Sources' in phone settings" -ForegroundColor White
    Write-Host "3. Install the APK" -ForegroundColor White
    Write-Host "4. The app will connect to: https://www.zainalabidin.pro" -ForegroundColor White
    
    Write-Host ""
    $openFolder = Read-Host "Open APK folder? (Y/N)"
    if ($openFolder -eq "Y" -or $openFolder -eq "y") {
        Start-Process "build\app\outputs\flutter-apk"
    }
} else {
    Write-Host "❌ ERROR: Failed to build APK" -ForegroundColor Red
    Write-Host "Check the errors above" -ForegroundColor Yellow
}

Write-Host "===============================================" -ForegroundColor Cyan
Read-Host "Press Enter to exit"
