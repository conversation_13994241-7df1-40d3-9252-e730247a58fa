{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d56e9cf4a741c027dc6b2389ec6e72e8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b1bf3f92aaf4d6d1d27483fb8049c01d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98df40e9aefbf2095ac2d99ae4d7186e94", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832098975236b79c82c957db4aaa4f8b1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98df40e9aefbf2095ac2d99ae4d7186e94", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987eb32f8f468ee837830bbd5d3e499f2c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9822ff3c75078213bc2ac36e645616ab96", "guid": "bfdfe7dc352907fc980b868725387e989e776eeb4bbfbc6db7293d0c53e87bb0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981cf722f2da5237283eb3b7037af5391e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a1b015417672bdcc4453fbd3861312b1", "guid": "bfdfe7dc352907fc980b868725387e982ee44d680bb2cd9f6909aea5f79f3f70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847fb87aa59bbde46ccee93cddaed10d5", "guid": "bfdfe7dc352907fc980b868725387e9838da186e7dced1a9e931560bdab8e855"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98945d362b9d2331edee7673ab7fcafcb2", "guid": "bfdfe7dc352907fc980b868725387e9884308544f1c52a526de864193e026311"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889ec9a426b09dd077fb6359f101faa16", "guid": "bfdfe7dc352907fc980b868725387e98121cfb3e736155e27437afea8bdfde0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863683af8c91741c4096b7bc68e5fe290", "guid": "bfdfe7dc352907fc980b868725387e989ffa41dedcc67f842b89b95ce5cf254f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d4963a4baf7266cca0daae234523859", "guid": "bfdfe7dc352907fc980b868725387e984b82d835a59cb8cd2c8c0bc56c19d0df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af7472b37cc219433068f96fbce803f0", "guid": "bfdfe7dc352907fc980b868725387e987249361db9bc4ae7a1f8b0357176bc2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfdf603650deb5c01944b93a77d78e8a", "guid": "bfdfe7dc352907fc980b868725387e98c121ccde7e7cbc7c516c1a61c5f2da56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d78166a9a12b778ee0b18b57320ef7f", "guid": "bfdfe7dc352907fc980b868725387e9854afb569bd613addcaec87151d413214"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d032f8aab6c040d4f047532f0c4878cc", "guid": "bfdfe7dc352907fc980b868725387e9841e37de71e3f691ecd554f898f7d2b2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c23f483010b75525d144966c92b849c", "guid": "bfdfe7dc352907fc980b868725387e986d5dce5849a8b2c659b2cb90ae2e6b93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddfd4c1b28fe4dc674803782a728129a", "guid": "bfdfe7dc352907fc980b868725387e9873ffdd552af69b9dd4d43e99995e2551"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c317d3c8a2cd0e74e795511b20766f8", "guid": "bfdfe7dc352907fc980b868725387e98c2a2c8d2ffeac316ad7578d091a52339"}], "guid": "bfdfe7dc352907fc980b868725387e984b911dd180e468f3133f2f9f7ad4668c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c7bf7ee5fbc558c481104fe48eb4be0", "guid": "bfdfe7dc352907fc980b868725387e988bcff1117c05b170acd2f17906f050ec"}], "guid": "bfdfe7dc352907fc980b868725387e988fdcdc9f1edab5f14c98f96a5fd5bede", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f9d16bf3393dbb6b19c0fe4b80bca7ab", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e982fd664e636d6d158625ade98ade60eab", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}