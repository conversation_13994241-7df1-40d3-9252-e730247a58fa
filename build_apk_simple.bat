@echo off
echo ===============================================
echo         Build APK for Mobile App
echo ===============================================
echo.

echo Step 1: Check Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed!
    echo Please install Flutter first
    echo Download from: https://flutter.dev
    pause
    exit /b 1
)

echo.
echo Step 2: Navigate to app directory...
cd "User app and web"
if %errorlevel% neq 0 (
    echo ERROR: Cannot find app directory
    echo Make sure you are in the correct folder
    pause
    exit /b 1
)

echo.
echo Step 3: Clean project...
flutter clean

echo.
echo Step 4: Get dependencies...
flutter pub get

echo.
echo Step 5: Build Release APK...
flutter build apk --release

echo.
echo ===============================================
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo SUCCESS: APK built successfully!
    echo.
    echo APK Location:
    echo build\app\outputs\flutter-apk\app-release.apk
    echo.
    echo Opening APK folder...
    explorer "build\app\outputs\flutter-apk"
) else (
    echo ERROR: Failed to build APK
    echo Check the errors above
)

echo ===============================================
pause
