class Images {
  static String get logo => 'logo'.png;
  static String get webAppbarLogo => 'webAppbarLogo'.png;
  static String get placeholder => 'placeholder'.jpg;
  static String get emptyService => 'empty_service'.png;
  static String get emptyProvider => 'empty_provider'.png;
  static String get emptySearchService => 'empty_search_service'.png;
  static String get emptyCart => 'empty_cart'.png;
  static String get emptyNotification => 'empty_notification'.png;
  static String get emptyAddress => 'empty_address'.png;
  static String get cart => 'cart'.png;
  static String get home => 'home'.png;
  static String get menu => 'menu'.png;
  static String get bookings => 'bookings'.png;
  static String get guest => 'guest'.png;
  static String get maintenance => 'maintenance'.png;
  static String get customerCare => 'customer_care'.png;
  static String get chatImage => 'chat_image'.png;
  static String get edit => 'edit'.png;
  static String get facebook => 'facebook'.png;
  static String get google => 'google'.png;
  static String get logout => 'logout'.png;
  static String get marker => 'marker'.png;
  static String get emptyCoupon => 'empty_coupon'.png;
  static String get noInternet => 'no_internet'.png;
  static String get notificationSmall => 'notification_icon_small'.png;
  static String get profile => 'profile'.png;
  static String get successIcon => 'success_icon'.png;
  static String get us => 'us'.png;
  static String get ar => 'arabic'.png;
  static String get bn => 'bangladesh'.png;
  static String get india => 'india'.png;
  static String get update => 'update'.png;
  static String get warning => 'warning'.png;
  static String get mapLocation => 'map_location'.png;
  static String get cancel => 'cancel_icon'.png;
  static String get offerBanner => 'offer_banner'.png;
  static String get webSignInButton => 'web_signin_button'.png;
  static String get orderDetailsSelected => 'order_details_selected'.png;
  static String get orderDetailsUnselected => 'order_details_unselected'.png;
  static String get paymentSelected => 'payment_selected'.png;
  static String get paymentUnSelected => 'payment_unselected'.png;
  static String get completeSelected => 'complete_selected'.png;
  static String get completeUnSelected => 'complete_unselected'.png;
  static String get couponLogo => 'coupon_logo'.png;
  static String get starIcon => 'star_icon'.png;
  static String get starFill => 'star_fill'.png;
  static String get starBorder => 'star_border'.png;
  static String get editButton => 'edit_button'.png;
  static String get cod => 'cod'.png;
  static String get pay => 'cash_on_delivery_icon'.png;
  static String get profileIcon => 'profile_icon'.png;
  static String get file=> 'file_icon'.png;
  static String get image=> 'image_icon'.png;
  static String get sendMessage=> 'send_message_icon'.png;
  static String get arabicTwo => 'arabic_two'.png;
  static String get address=> 'address'.png;
  static String get notification=> 'profile_notification'.png;
  static String get settings=> 'settings'.png;
  static String get homeProfile=> 'home_profile'.png;
  static String get office=> 'office'.png;
  static String get forgotPass=> 'forgot_pass'.png;
  static String get translate => 'select_language'.png;
  static String get editPen => 'edit_pen'.png;
  static String get cartDelete => 'cart_delete'.png;
  static String get cartDeleteVariation => 'cart_delete_variation'.png;
  static String get accountDelete => 'cart_delete_variation'.png;
  static String get deleteProfile => 'deleteAccount'.png;
  static String get voucherImage => 'voucher_image'.png;
  static String get helpIcon => 'help_support_icon'.png;
  static String get cancellationPolicy => 'cancellation_policy'.png;
  static String get refundPolicy => 'refund_policy'.png;
  static String get logoutIcon => 'logout_icon'.png;
  static String get privacyPolicyIcon => 'privacy_icon'.png;
  static String get termsIcon => 'terms_icon'.png;
  static String get voucherIcon => 'voucher_icon'.png;
  static String get calendar1 => 'calendar_1'.png;
  static String get calendar2 => 'calendar_2'.png;
  static String get calendar3 => 'calendar_3'.png;
  static String get iconLocation => 'icon_location'.png;
  static String get emptyReview => 'empty_review'.png;
  static String get reviewTopBanner => 'review_top_banner'.png;
  static String get offerMenu => 'offers_menu'.png;
  static String get orderComplete => 'order_complete'.png;
  static String get aboutUs => 'about_us'.png;
  static String get folder => 'folder'.png;
  static String get couponIcon => 'coupon_icon'.png;
  static String get bookingsIcon => 'bookings_icon'.png;
  static String get emptyOffer => 'empty_offer'.png;
  static String get helpAndSupport => 'help_and_support'.png;
  static String get emptyBooking => 'empty_booking'.png;
  static String get webHomeIcon => 'web_home_icon'.png;
  static String get webCartIcon => 'web_cart_icon'.png;
  static String get footerAddress => 'footer_address'.png;
  static String get suggestServiceIcon => 'suggest_service_icon'.png;
  static String get homeProviderBackground => 'home_provider_background'.png;
  static String get walletBackground => 'wallet_background'.png;
  static String get walletSmall => 'wallet_small'.png;
  static String get myPoint => 'my_point'.png;
  static String get convertPoint => 'convert_point'.png;
  static String get info => 'info'.png;
  static String get loyaltyPointBackground => 'loyalty_point_background'.png;
  static String get emptyTransaction => 'empty_transaction'.png;
  static String get referAndEarn => 'refer_and_earn'.png;
  static String get iMark => 'i_mark'.png;
  static String get filter => 'filter'.png;
  static String get sort => 'sort_icon'.png;
  static String get hot => 'hot'.png;
  static String get reviewIcon => 'review_icon'.png;
  static String get providerImage => 'provider_image'.png;
  static String get walletMenu => 'wallet_menu'.png;
  static String get shareIcon => 'share_icon'.png;
  static String get homeCreatePostMan => 'home_create_post_man'.png;
  static String get bottomCreatePostMan => 'bottom_create_post_man'.png;
  static String get messageIcon => 'message_icon'.png;
  static String get customPostIcon => 'custom_post_icon'.png;
  static String get rightMark => 'right_mark'.png;
  static String get personIcon => 'person_icon'.png;
  static String get ignore => 'ignore'.png;
  static String get walletBannerBackground => 'wallet_banner_background'.png;
  static String get note => 'note'.png;
  static String get offlinePayment => 'offline_payment'.png;
  static String get trackService => 'track_service'.png;
  static String get accepted => 'accepted'.png;
  static String get ongoing => 'ongoing'.png;
  static String get canceled => 'canceled'.png;
  static String get completed => 'completed'.png;
  static String get adminChat => 'admin_chat'.png;
  static String get notAvailableIcon => 'not_available_icon'.png;
  static String get areaTopIcon => 'area_top_icon'.png;
  static String get areaMenuIcon => 'area_menu_icon'.png;
  static String get providerNotAvailable => 'provider_not_avilable'.png;
  static String get servicePrice => 'service_price'.png;
  static String get downloadImage => 'download_icon'.png;
  static String get serviceNotAvailable => 'service_not_available'.png;
  static String get createPostAddressBackground => 'create_post_address_background'.png;
  static String get popularServicesBackgroundImage => 'popular_services_background_image'.png;
  static String get createPostBackgroundImage => 'create_post_background_image'.png;
  static String get searchIcon => 'search'.png;
  static String get emptyConversation => 'empty_conversation'.png;
  static String get fileIcon => 'file'.png;
  static String get upload => 'upload'.png;
  static String get imageIcon => 'image'.png;
  static String get sendIcon => 'send_icon'.png;
  static String get cancelIcon => 'cancel_icon'.png;
  static String get correctIcon => 'correct_icon'.png;
  static String get couponWarning => 'coupon_warning'.png;
  static String get adminPlaceHolder => 'admin_placeholder'.png;
  static String get userPlaceHolder => 'user_placeholder'.png;
  static String get replaceCoupon => 'replace_coupon'.png;
  static String get onBoardingTop => 'onboard_top'.png;
  static String get onBoardingTopTwo => 'onboard_top_2'.png;
  static String get onBoardingBottomThree => 'onboard_bottom_three'.png;
  static String get onBoardTopOne => 'onboard_top_one'.png;
  static String get onBoardTopTwo => 'onboard_top_two'.png;
  static String get myFavorite => 'my_favorite'.png;
  static String get favoriteProvider => 'favorite_provider'.png;
  static String get favoriteService => 'favorite_service'.png;
  static String get welcomeIcon => 'welcome_icon'.png;
  static String get scheduleIcon => 'schedule_icon'.png;
  static String get highlightProvider => 'highlight_provider'.png;
  static String get exploreProvider => 'explore_provider'.png;
  static String get mapBackground => 'map_bg'.png;
  static String get currentLocation => 'current_location'.png;
  static String get currentLocationIos => 'current_location'.png;
  static String get selectedProvider => 'selected_provider'.png;
  static String get unselectedProvider=> 'unselected_provider'.png;
  static String get recommendedServiceBg => 'recommended_service'.png;
  static String get share => 'share'.png;
  static String get otp => 'otp'.png;
  static String get verified => 'verified'.png;
  static String get error => 'error'.png;
  static String get error404 => 'error_404'.png;
  static String get reviewReply => 'review_reply'.png;
  static String get mapIconExtended => 'map-picker-1'.json;
  static String get mapIconMinimised => 'map-picker-2'.json;
  static String get providerUnavailable => 'provider_unavailable'.png;
  static String get noProvider => 'no-provider'.png;
  static String get noProviderBg => 'no-provider-bg'.png;
  static String get distance => 'distance'.png;
  static String get favorite => 'favorite'.png;
  static String get unFavorite => 'unfavorite'.png;
  static String get copyCouponIcon => 'copy_coupon_icon'.png;
  static String get appliedCouponPercentIcon => 'applied_coupon_percent_icon'.png;
  static String get footerBg => 'footer_bg'.png;


  ///web social icon
  static get facebookIcon => 'facebook_icon'.png;
  static get twitterIcon => 'twitter_icon'.png;
  static get youtubeIcon => 'youtube_icon'.png;
  static get linkedinIcon => 'linkedin_icon'.png;
  static get instagramIcon => 'instagram_icon'.png;
  static get pinterestIcon => 'pinterest_icon'.png;
  static get playStoreIcon => 'play_store'.png;
  static get appStoreIcon => 'app_store'.png;
  static String get apple => 'apple'.png;

}

extension on String {
  String get png => 'assets/images/$this.png';
  String get jpg => 'assets/images/$this.jpg';
  String get json => 'assets/json/$this.json';
}
