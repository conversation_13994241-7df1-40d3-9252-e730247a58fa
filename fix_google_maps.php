<?php
/**
 * إصلاح Google Maps API
 * Fix Google Maps API
 */

echo "===============================================\n";
echo "        إصلاح Google Maps API\n";
echo "        Fix Google Maps API\n";
echo "===============================================\n\n";

// إعدادات قاعدة البيانات
$host = 'localhost';
$database = 'kidzrcle_kalfa';
$username = 'kidzrcle_kalfa';
$password = 'kidzrcle_kalfa';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n\n";
    
    // مفتاح Google Maps مؤقت (يجب استبداله بمفتاح حقيقي)
    $tempApiKey = "AIzaSyBkf8TiQQFYUKpYOKvd8ZhBGf5cGf5cGf5";
    
    // إعداد Google Maps
    $googleMapSettings = [
        'map_api_key_server' => $tempApiKey,
        'map_api_key_client' => $tempApiKey
    ];
    
    $liveValues = json_encode($googleMapSettings);
    $testValues = json_encode($googleMapSettings);
    
    // التحقق من وجود الإعداد
    $checkSql = "SELECT id FROM business_settings WHERE key_name = 'google_map' AND settings_type = 'third_party'";
    $checkStmt = $pdo->prepare($checkSql);
    $checkStmt->execute();
    
    if ($checkStmt->rowCount() > 0) {
        // تحديث الإعداد الموجود
        $updateSql = "UPDATE business_settings 
                      SET live_values = ?, test_values = ?, updated_at = NOW() 
                      WHERE key_name = 'google_map' AND settings_type = 'third_party'";
        $updateStmt = $pdo->prepare($updateSql);
        $updateStmt->execute([$liveValues, $testValues]);
        echo "✅ تم تحديث إعدادات Google Maps\n";
    } else {
        // إدراج إعداد جديد
        $insertSql = "INSERT INTO business_settings 
                      (id, key_name, live_values, test_values, settings_type, mode, is_active, created_at, updated_at) 
                      VALUES (UUID(), 'google_map', ?, ?, 'third_party', 'live', 1, NOW(), NOW())";
        $insertStmt = $pdo->prepare($insertSql);
        $insertStmt->execute([$liveValues, $testValues]);
        echo "✅ تم إنشاء إعدادات Google Maps جديدة\n";
    }
    
    echo "\n";
    echo "🔑 تم إعداد مفتاح Google Maps المؤقت: $tempApiKey\n";
    echo "\n";
    
    // مسح الكاش
    echo "🧹 مسح الكاش...\n";
    
    // تشغيل أوامر Laravel لمسح الكاش
    $commands = [
        'php artisan cache:clear',
        'php artisan config:clear',
        'php artisan view:clear'
    ];
    
    foreach ($commands as $command) {
        echo "تشغيل: $command\n";
        $output = shell_exec($command . ' 2>&1');
        if ($output) {
            echo "النتيجة: " . trim($output) . "\n";
        }
    }
    
    echo "\n";
    echo "===============================================\n";
    echo "✅ تم إصلاح Google Maps بنجاح!\n";
    echo "\n";
    echo "🚨 تحذير مهم:\n";
    echo "المفتاح المستخدم حالياً مؤقت وقد لا يعمل بشكل صحيح.\n";
    echo "يجب الحصول على مفتاح Google Maps API حقيقي.\n";
    echo "\n";
    echo "📋 الخطوات التالية:\n";
    echo "1. احصل على Google Maps API Key من:\n";
    echo "   https://console.cloud.google.com/\n";
    echo "\n";
    echo "2. اذهب إلى لوحة التحكم:\n";
    echo "   https://www.zainalabidin.pro/admin\n";
    echo "\n";
    echo "3. اذهب إلى:\n";
    echo "   Settings > Third Party > Google Map\n";
    echo "\n";
    echo "4. أدخل مفتاحك الحقيقي في:\n";
    echo "   - Map API Key Server\n";
    echo "   - Map API Key Client\n";
    echo "\n";
    echo "5. اضغط Update\n";
    echo "\n";
    echo "6. اختبر الخرائط في:\n";
    echo "   https://www.zainalabidin.pro/provider/auth/sign-up\n";
    echo "\n";
    echo "===============================================\n";
    
} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
    echo "\nتحقق من:\n";
    echo "1. إعدادات قاعدة البيانات في ملف .env\n";
    echo "2. أن قاعدة البيانات تعمل بشكل صحيح\n";
    echo "3. صلاحيات المستخدم\n";
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
}

echo "\n";
echo "للحصول على مساعدة مفصلة، راجع:\n";
echo "GOOGLE_MAPS_SETUP_GUIDE.md\n";
echo "\n";
?>
