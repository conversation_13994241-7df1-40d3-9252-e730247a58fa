{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ab757f4d93df5b6f893146233868f865", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9840908e331abae8f6b949106c89823fa8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983c24b5c18b7fb99dd923d896ea6970b0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9825012cd7844b7ff8aac05592e5dda870", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983c24b5c18b7fb99dd923d896ea6970b0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98156e02de1222d40315a525367071825b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f5312523c72b66f784b4c3c15120894b", "guid": "bfdfe7dc352907fc980b868725387e98dc31e03501de814d85419a8db5f7813a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe9a9127cdfde575267ca5628c459727", "guid": "bfdfe7dc352907fc980b868725387e98d0b45cdd3750f03d1040cf58b98c3ca1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842e85ca5c8cde8a4705e04c7c0317c5d", "guid": "bfdfe7dc352907fc980b868725387e98f145c538c2be01e9a18b7c2b5acd5618", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981824b6125b74dbd691bf2ca148c127f5", "guid": "bfdfe7dc352907fc980b868725387e98947f10b0ed49b90ba557d44bd98bf18d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7fc3b48ab8ee7694d339b7b8b855292", "guid": "bfdfe7dc352907fc980b868725387e9823da611acc304e8af28cb24a756f06d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982de1c27a03c18d4ac29b634c82a40907", "guid": "bfdfe7dc352907fc980b868725387e9818e6953f9c6c7502bc71d375f7ded9d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8662d6b8e03fe17f92a98e80c77eafe", "guid": "bfdfe7dc352907fc980b868725387e98a0a554f8f8d8395fb87cabdd6f52b444", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d065389c3845c536fe798507d9cfa066", "guid": "bfdfe7dc352907fc980b868725387e982ef2067b4f50c443b41812f5ef24d61d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e618f18335e5db3f22649dfec29c5328", "guid": "bfdfe7dc352907fc980b868725387e9896f0c4197c00ec849ea72d075c2c535c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d8db648027adadf6393661fed3bdb78", "guid": "bfdfe7dc352907fc980b868725387e98d1956363adb6854f511f2e98654655fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d6d5bbf4428c2156939ca6b7cd79f54", "guid": "bfdfe7dc352907fc980b868725387e98e2b65eb621fec320abe27704d10f956c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98fe9ec28bec2ed2ced182b5fa0e99a771", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6bcae1a7a37e9de5116e844b84d1d5e", "guid": "bfdfe7dc352907fc980b868725387e987bcaa836d1d3de745d3b6e86a1813eb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826580cfa536116fd5d84ca4db05a826c", "guid": "bfdfe7dc352907fc980b868725387e984230e55be7e8bbc9870a976e95f8a767"}], "guid": "bfdfe7dc352907fc980b868725387e98da0224dbd5c087a35abbd37325cc85f8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c7bf7ee5fbc558c481104fe48eb4be0", "guid": "bfdfe7dc352907fc980b868725387e98264543ad42aa808b0adf982aedf2d77e"}], "guid": "bfdfe7dc352907fc980b868725387e986065964ab57e94e62d380cd4a8459f5d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9833c837519683d3d4372af713609a39ed", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e98e3b6eaf2f26ef0829eb9da5b2989a2a2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}