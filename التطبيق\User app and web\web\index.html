<!DOCTYPE html>
<html lang="en-US">
<head>
  <base href="/">

  <meta name="google-signin-client_id" content="YOUR_CLIENT_ID">
  <meta charset="UTF-8">

  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A multi-vendor service providing app.">
  <meta name="robots" content="nofollow, noindex, max-snippet:1, max-video-preview:1, max-image-preview:standard">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Demandium">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>Demandium</title>
  <link rel="manifest" href="manifest.json">
  <link rel="stylesheet" type="text/css" href="style.css">

  <script type="application/javascript" src="/assets/packages/flutter_inappwebview_web/assets/web/web_support.js" defer></script>

  <link rel="canonical" href="https://demandium-web.6amtech.com/" />

  <style>.grecaptcha-badge { visibility: hidden;} </style>

  <script>
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = {{flutter_service_worker_version}};
  </script>

  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/service_worker.js')
          .then((registration) => {
            console.log('ServiceWorker registration successful with scope: ', registration.scope);
          })
          .catch((error) => {
            console.log('ServiceWorker registration failed: ', error);
          });
      });
    }
  </script>

  <script>
    if (
      navigator.userAgent.indexOf("Safari") !== -1 &&
      navigator.userAgent.indexOf("Chrome") === -1
    ) {
      var originalGetContext = HTMLCanvasElement.prototype.getContext;
      HTMLCanvasElement.prototype.getContext = function () {
        var contextType = arguments[0];
        if (contextType === "webgl2") {
          return;
        }
        return originalGetContext.apply(
          this,
          [contextType].concat(Array.prototype.slice.call(arguments, 1))
        );
      };
    }
  </script>


  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js?version=3.2.0" defer></script>

</head>
<body>

<script async src="https://maps.googleapis.com/maps/api/js?key=YOUR_MAP_KEY&loading=async&callback=Function.prototype"></script>


  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js"></script>

  <div class="preloader">
    <div class="preloader-container">
      <img width="60" class="preloader-img" src="logo.png" alt="">
      <div class="loader"></div>
    </div>
  </div>


  <header class="header">
    <div class="header-container d-flex align-items-center justify-content-between">
      <div class="header-start d-flex gap-5 align-items-center">
        <img class="logo" src="appbarlogo.png" alt="">
        <div class="placeholder placeholder-wide"></div>
        <div class="placeholder"></div>
        <div class="placeholder"></div>
        <div class="placeholder"></div>
      </div>
      <div class="header-end d-flex gap-5">
        <div class="placeholder"></div>
        <div class="placeholder"></div>
        <div class="placeholder"></div>
      </div>
    </div>
  </header>

  <script>
    document.querySelectorAll("img.svg").forEach(function (img) {
    var imgID = img.getAttribute("id");
    var imgClass = img.getAttribute("class");
    var imgURL = img.getAttribute("src");
    // Create a new XMLHttpRequest
    var xhr = new XMLHttpRequest();
    xhr.open("GET", imgURL, true);
    // Set the responseType to "document" to parse the response as an XML document
    xhr.responseType = "document";
    xhr.onload = function () {
      if (xhr.status === 200) {
        var data = xhr.response;
        // Get the SVG tag from the response, ignore the rest
        var svg = data.querySelector("svg");
        // Add replaced image's ID to the new SVG
        if (typeof imgID !== "undefined") {
          svg.setAttribute("id", imgID);
        }
        // Add replaced image's classes to the new SVG
        if (typeof imgClass !== "undefined") {
          svg.setAttribute("class", imgClass + " replaced-svg");
        }
        // Remove any invalid XML tags
        svg.removeAttribute("xmlns:a");
        // Check if the viewport is set; set it if not
        if (!svg.getAttribute("viewBox") && svg.getAttribute("height") && svg.getAttribute("width")) {
          svg.setAttribute("viewBox", "0 0 " + svg.getAttribute("height") + " " + svg.getAttribute("width"));
        }
        // Replace the image with the new SVG
        img.parentNode.replaceChild(svg, img);
      }
    };
    xhr.send();
  });
  </script>
  <script>
    window.addEventListener('load', function(ev) {
      {{flutter_js}}
      {{flutter_build_config}}

      _flutter.loader.load({
        serviceWorker: {
          serviceWorkerVersion: {{flutter_service_worker_version}},
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            if(document.getElementById('splash'))
              document.getElementById('splash').remove();
            appRunner.runApp();
          });
        }
      });
    });
  </script>

  <script>

    // Check if localStorage is supported in the browser
      if (typeof(Storage) !== "undefined") {
        // Get the item from localStorage
        var itemValue = localStorage.getItem("flutter.demand_theme");

        console.log("flutter.theme", itemValue)

        // Check if the item exists
        if (itemValue !== null) {
          // Check the value of the item
          if (itemValue === "true") {
            // If the value is "true", add a class to the body
            document.body.classList.add("theme-dark");
          } else {
            // If the value is not "true", remove the class from the body
            document.body.classList.remove("theme-light");
          }
        } else {
          console.log("Item not found in localStorage");
        }
      } else {
        console.log("localStorage is not supported in this browser");
      }

  </script>
  <script>
    // Import the functions you need from the SDKs you need
    import { initializeApp } from "firebase/app";
    import { getAnalytics } from "firebase/analytics";
    // TODO: Add SDKs for Firebase products that you want to use
    // https://firebase.google.com/docs/web/setup#available-libraries

    // Your web app's Firebase configuration
    // For Firebase JS SDK v7.20.0 and later, measurementId is optional
    const firebaseConfig = {
      apiKey: "AIzaSyATwpBSYz69b5Y9ryQLELOJIHZSpJcXf7I",
      authDomain: "demancms.firebaseapp.com",
      projectId: "demancms",
      storageBucket: "demancms.appspot.com",
      messagingSenderId: "889759666168",
      appId: "1:889759666168:web:ab661cb341d3e47384d00d"
    };

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const analytics = getAnalytics(app);
  </script>

</body>
</html>
