# كيفية الحصول على معلومات قاعدة البيانات من الاستضافة
# How to Get Database Information from Hosting

## 🔍 تحتاج لمعرفة هذه المعلومات من استضافة موقعك:

### 1. **اسم قاعدة البيانات (Database Name)**
```
مثال: username_demandium
أو: zainalabidin_main
```

### 2. **اسم مستخدم قاعدة البيانات (Database Username)**
```
مثال: username_dbuser
أو: zainalabidin_user
```

### 3. **كلمة مرور قاعدة البيانات (Database Password)**
```
كلمة المرور التي أنشأتها عند إعداد قاعدة البيانات
```

### 4. **خادم قاعدة البيانات (Database Host)**
```
عادة: localhost
أو: mysql.yourdomain.com
```

---

## 📋 كيفية الحصول على هذه المعلومات:

### الطريقة 1: من cPanel
1. **اذهب إلى cPanel**
2. **ابحث عن "MySQL Databases"**
3. **ستجد قائمة بقواعد البيانات الموجودة**
4. **ستجد أيضاً قائمة بالمستخدمين**

### الطريقة 2: من لوحة تحكم الاستضافة
1. **اذهب إلى قسم قواعد البيانات**
2. **ابحث عن "Database Management"**
3. **ستجد المعلومات المطلوبة**

### الطريقة 3: اتصل بالدعم الفني
1. **اتصل بدعم الاستضافة**
2. **اطلب معلومات قاعدة البيانات**
3. **سيعطونك المعلومات المطلوبة**

---

## ⚙️ بعد الحصول على المعلومات:

### عدل ملف .env بالمعلومات الصحيحة:
```env
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=اسم_قاعدة_البيانات_الصحيح
DB_USERNAME=اسم_المستخدم_الصحيح
DB_PASSWORD=كلمة_المرور_الصحيحة
```

### ثم شغل:
```bash
final_fix.bat
```

---

## 🚨 إذا لم تكن قاعدة البيانات موجودة:

### أنشئ قاعدة بيانات جديدة:
1. **اذهب إلى cPanel**
2. **اختر "MySQL Databases"**
3. **أنشئ قاعدة بيانات جديدة:**
   - الاسم: `demandium` أو `zainalabidin_db`
4. **أنشئ مستخدم جديد**
5. **اربط المستخدم بقاعدة البيانات**
6. **امنح جميع الصلاحيات**

---

## 📞 تحتاج مساعدة؟

**أرسل لي هذه المعلومات:**
1. نوع الاستضافة (cPanel, Plesk, etc.)
2. هل قاعدة البيانات موجودة؟
3. ما هي رسالة الخطأ الحالية؟

**وسأساعدك في الحصول على المعلومات الصحيحة!**

---

## ✅ بمجرد إصلاح قاعدة البيانات:

- الموقع سيعمل بشكل طبيعي
- التطبيق سيتصل بالموقع
- جميع الوظائف ستعمل

**المشكلة الوحيدة الآن هي معلومات قاعدة البيانات!** 🎯
