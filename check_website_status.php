<?php
/**
 * ملف فحص حالة الموقع
 * Website Status Check Script
 */

echo "===============================================\n";
echo "        فحص حالة الموقع\n";
echo "        Website Status Check\n";
echo "===============================================\n\n";

// ألوان للنص في Terminal
class Colors {
    public static $GREEN = "\033[0;32m";
    public static $RED = "\033[0;31m";
    public static $YELLOW = "\033[1;33m";
    public static $BLUE = "\033[0;34m";
    public static $NC = "\033[0m"; // No Color
}

function printStatus($message) {
    echo Colors::$GREEN . "✅ " . $message . Colors::$NC . "\n";
}

function printError($message) {
    echo Colors::$RED . "❌ " . $message . Colors::$NC . "\n";
}

function printWarning($message) {
    echo Colors::$YELLOW . "⚠️  " . $message . Colors::$NC . "\n";
}

function printInfo($message) {
    echo Colors::$BLUE . "ℹ️  " . $message . Colors::$NC . "\n";
}

// فحص متطلبات PHP
printInfo("فحص متطلبات PHP...");

$phpVersion = phpversion();
if (version_compare($phpVersion, '8.0.0', '>=')) {
    printStatus("إصدار PHP: $phpVersion");
} else {
    printError("إصدار PHP قديم: $phpVersion (مطلوب 8.0 أو أحدث)");
}

// فحص الإضافات المطلوبة
$requiredExtensions = [
    'bcmath', 'ctype', 'fileinfo', 'json', 'mbstring', 
    'openssl', 'pdo', 'tokenizer', 'xml', 'zip', 'gd', 'curl'
];

$missingExtensions = [];
foreach ($requiredExtensions as $extension) {
    if (extension_loaded($extension)) {
        printStatus("إضافة $extension: مُثبتة");
    } else {
        printError("إضافة $extension: غير مُثبتة");
        $missingExtensions[] = $extension;
    }
}

if (empty($missingExtensions)) {
    printStatus("جميع إضافات PHP مُثبتة");
} else {
    printWarning("إضافات مفقودة: " . implode(', ', $missingExtensions));
}

echo "\n";

// فحص ملف .env
printInfo("فحص ملف .env...");
if (file_exists('.env')) {
    printStatus("ملف .env موجود");
    
    // قراءة إعدادات .env
    $envContent = file_get_contents('.env');
    $envLines = explode("\n", $envContent);
    $envVars = [];
    
    foreach ($envLines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with(trim($line), '#')) {
            list($key, $value) = explode('=', $line, 2);
            $envVars[trim($key)] = trim($value);
        }
    }
    
    // فحص المتغيرات المهمة
    $importantVars = ['APP_URL', 'DB_DATABASE', 'DB_USERNAME', 'APP_KEY'];
    foreach ($importantVars as $var) {
        if (isset($envVars[$var]) && !empty($envVars[$var])) {
            if ($var === 'APP_KEY') {
                printStatus("$var: مُعرف");
            } else {
                printStatus("$var: " . $envVars[$var]);
            }
        } else {
            printError("$var: غير مُعرف أو فارغ");
        }
    }
} else {
    printError("ملف .env غير موجود");
}

echo "\n";

// فحص صلاحيات المجلدات
printInfo("فحص صلاحيات المجلدات...");
$directories = ['storage', 'bootstrap/cache'];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            printStatus("مجلد $dir: قابل للكتابة");
        } else {
            printError("مجلد $dir: غير قابل للكتابة");
        }
    } else {
        printError("مجلد $dir: غير موجود");
    }
}

echo "\n";

// فحص الاتصال بقاعدة البيانات
printInfo("فحص الاتصال بقاعدة البيانات...");
try {
    // تحميل Laravel
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    // اختبار الاتصال
    $pdo = DB::connection()->getPdo();
    printStatus("الاتصال بقاعدة البيانات: ناجح");
    
    // فحص الجداول
    $tables = DB::select('SHOW TABLES');
    if (count($tables) > 0) {
        printStatus("عدد الجداول: " . count($tables));
    } else {
        printWarning("لا توجد جداول في قاعدة البيانات");
    }
    
} catch (Exception $e) {
    printError("فشل الاتصال بقاعدة البيانات: " . $e->getMessage());
}

echo "\n";

// فحص ملفات مهمة
printInfo("فحص الملفات المهمة...");
$importantFiles = [
    'public/index.php' => 'ملف الدخول الرئيسي',
    'artisan' => 'ملف Artisan',
    'composer.json' => 'ملف Composer',
    'public/.htaccess' => 'ملف Apache Rewrite'
];

foreach ($importantFiles as $file => $description) {
    if (file_exists($file)) {
        printStatus("$description: موجود");
    } else {
        printWarning("$description: غير موجود");
    }
}

echo "\n";

// فحص الرابط الرمزي للتخزين
printInfo("فحص رابط التخزين...");
if (is_link('public/storage')) {
    printStatus("رابط التخزين: موجود");
} else {
    printWarning("رابط التخزين: غير موجود - قم بتشغيل: php artisan storage:link");
}

echo "\n";

// فحص إعدادات الأمان
printInfo("فحص إعدادات الأمان...");
if (isset($envVars['APP_DEBUG']) && $envVars['APP_DEBUG'] === 'false') {
    printStatus("وضع التطوير: مُعطل (آمن)");
} else {
    printWarning("وضع التطوير: مُفعل (غير آمن للإنتاج)");
}

if (isset($envVars['APP_ENV']) && $envVars['APP_ENV'] === 'production') {
    printStatus("بيئة التطبيق: إنتاج");
} else {
    printWarning("بيئة التطبيق: ليست إنتاج");
}

echo "\n";
echo "===============================================\n";
printInfo("انتهى فحص حالة الموقع");
echo "===============================================\n";
?>
