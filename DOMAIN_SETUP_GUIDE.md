# دليل ربط التطبيق بالموقع
# Domain Setup Guide

## 📁 الملفات المساعدة المتوفرة:

### 1. ملفات الإعداد التلقائي:
- `setup_domain.sh` (لأنظمة Linux/Mac)
- `setup_domain.bat` (لنظام Windows)

### 2. ملف فحص الحالة:
- `check_website_status.php` (لفحص حالة الموقع)

### 3. دليل الإعداد:
- `DOMAIN_SETUP_GUIDE.md` (هذا الملف)

## 🌐 طرق ربط التطبيق بالموقع

### الطريقة الأولى: استخدام الدومين الرئيسي
**مثال:** `https://yourdomain.com`

### الطريقة الثانية: استخدام subdomain
**مثال:** `https://app.yourdomain.com`

### الطريقة الثالثة: استخدام مجلد فرعي
**مثال:** `https://yourdomain.com/app`

## 🚀 الطريقة السريعة (باستخدام الملفات المساعدة):

### لأنظمة Linux/Mac:
```bash
chmod +x setup_domain.sh
./setup_domain.sh
```

### لنظام Windows:
انقر مرتين على `setup_domain.bat`

### فحص حالة الموقع:
```bash
php check_website_status.php
```

---

## 📋 الخطوات المطلوبة:

### 1. إعداد الخادم (Server Setup)

#### أ) متطلبات الخادم:
- **PHP:** 8.0 أو أحدث
- **MySQL:** 5.7 أو أحدث
- **Apache/Nginx:** مع mod_rewrite مُفعل
- **Composer:** لإدارة التبعيات
- **SSL Certificate:** للأمان (مُنصح به)

#### ب) إعدادات PHP المطلوبة:
```ini
memory_limit = 256M
max_execution_time = 300
upload_max_filesize = 100M
post_max_size = 100M
```

### 2. رفع الملفات

#### أ) رفع ملفات التطبيق:
1. ارفع جميع ملفات المشروع إلى الخادم
2. تأكد من رفع مجلد `public` إلى المجلد الصحيح

#### ب) هيكل المجلدات على الخادم:
```
/home/<USER>/
├── public_html/          (أو www أو htdocs)
│   ├── index.php         (من مجلد public)
│   ├── assets/
│   └── ...
├── app/
├── config/
├── vendor/
└── ...
```

### 3. إعداد ملف .env

#### أ) نسخ ملف الإعدادات:
```bash
cp .env.example .env
```

#### ب) تحديث إعدادات الدومين:
```env
APP_NAME="اسم التطبيق"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# إعدادات قاعدة البيانات
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=database_name
DB_USERNAME=database_user
DB_PASSWORD=database_password

# إعدادات البريد الإلكتروني
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# إعدادات الأمان
FORCE_HTTPS=true
```

### 4. إعداد قاعدة البيانات

#### أ) إنشاء قاعدة البيانات:
```sql
CREATE DATABASE your_database_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### ب) إنشاء مستخدم قاعدة البيانات:
```sql
CREATE USER 'your_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON your_database_name.* TO 'your_user'@'localhost';
FLUSH PRIVILEGES;
```

### 5. تشغيل أوامر Laravel

```bash
# إنشاء مفتاح التطبيق
php artisan key:generate

# تشغيل migrations
php artisan migrate

# إنشاء symbolic link للتخزين
php artisan storage:link

# تحسين الأداء
php artisan config:cache
php artisan route:cache
php artisan view:cache

# إعداد الصلاحيات
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

### 6. إعداد الخادم

#### أ) Apache (.htaccess):
```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteRule ^(.*)$ public/$1 [L]
</IfModule>
```

#### ب) Nginx:
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/your/project/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### 7. إعداد SSL Certificate

#### أ) استخدام Let's Encrypt (مجاني):
```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-apache

# الحصول على شهادة SSL
sudo certbot --apache -d yourdomain.com
```

#### ب) تحديث .env للـ HTTPS:
```env
APP_URL=https://yourdomain.com
FORCE_HTTPS=true
```

---

## 🔧 إعدادات إضافية

### 1. إعداد Cron Jobs:
```bash
# إضافة إلى crontab
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1
```

### 2. إعداد Queue Workers:
```bash
# إنشاء supervisor config
sudo nano /etc/supervisor/conf.d/laravel-worker.conf
```

### 3. تحسين الأداء:
```bash
# تفعيل OPcache
echo "opcache.enable=1" >> /etc/php/8.0/apache2/php.ini

# ضغط الملفات
echo "LoadModule deflate_module modules/mod_deflate.so" >> /etc/apache2/apache2.conf
```

---

## ✅ التحقق من التثبيت

### 1. فحص الموقع:
- تصفح إلى `https://yourdomain.com`
- تأكد من ظهور الصفحة الرئيسية
- اختبر تسجيل الدخول للإدارة

### 2. فحص الأخطاء:
```bash
# فحص logs
tail -f storage/logs/laravel.log

# فحص Apache/Nginx logs
tail -f /var/log/apache2/error.log
```

### 3. اختبار الوظائف:
- تسجيل مستخدم جديد
- رفع الصور
- إرسال البريد الإلكتروني
- اختبار API

---

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ 500 Internal Server Error:
```bash
# فحص الصلاحيات
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/

# فحص .env
php artisan config:clear
```

#### 2. مشاكل الصور:
```bash
# إنشاء symbolic link
php artisan storage:link

# فحص الصلاحيات
chmod -R 755 storage/app/public/
```

#### 3. مشاكل قاعدة البيانات:
```bash
# اختبار الاتصال
php artisan tinker
DB::connection()->getPdo();
```

---

## 📞 الدعم الفني

إذا واجهت أي مشاكل:
1. تحقق من logs الأخطاء
2. تأكد من متطلبات الخادم
3. راجع إعدادات .env
4. اختبر الاتصال بقاعدة البيانات

تم إنشاء هذا الدليل لمساعدتك في ربط التطبيق بالموقع بنجاح! 🎉
