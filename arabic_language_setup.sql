-- ملف SQL لتحويل النظام إلى اللغة العربية
-- Arabic Language Setup SQL File
-- يجب تشغيل هذا الملف بعد تثبيت النظام لتحويله إلى اللغة العربية

-- تحديث إعدادات اللغة في النظام
UPDATE `business_settings` 
SET `live_values` = '[
    {
        "id": 1,
        "name": "english",
        "direction": "ltr",
        "code": "en",
        "status": 1,
        "default": false
    },
    {
        "id": 2,
        "name": "arabic",
        "direction": "rtl",
        "code": "ar",
        "status": 1,
        "default": true
    }
]',
`test_values` = '[
    {
        "id": 1,
        "name": "english",
        "direction": "ltr",
        "code": "en",
        "status": 1,
        "default": false
    },
    {
        "id": 2,
        "name": "arabic",
        "direction": "rtl",
        "code": "ar",
        "status": 1,
        "default": true
    }
]'
WHERE `key_name` = 'system_language' AND `settings_type` = 'business_information';

-- إدراج إعدادات اللغة إذا لم تكن موجودة
INSERT IGNORE INTO `business_settings` (`id`, `key_name`, `live_values`, `test_values`, `settings_type`, `mode`, `is_active`, `created_at`, `updated_at`) 
VALUES (
    UUID(),
    'system_language',
    '[
        {
            "id": 1,
            "name": "english",
            "direction": "ltr",
            "code": "en",
            "status": 1,
            "default": false
        },
        {
            "id": 2,
            "name": "arabic",
            "direction": "rtl",
            "code": "ar",
            "status": 1,
            "default": true
        }
    ]',
    '[
        {
            "id": 1,
            "name": "english",
            "direction": "ltr",
            "code": "en",
            "status": 1,
            "default": false
        },
        {
            "id": 2,
            "name": "arabic",
            "direction": "rtl",
            "code": "ar",
            "status": 1,
            "default": true
        }
    ]',
    'business_information',
    'live',
    1,
    NOW(),
    NOW()
);

-- تحديث اللغة الافتراضية للمستخدمين الموجودين
UPDATE `users` SET `current_language_key` = 'ar' WHERE `current_language_key` = 'en' OR `current_language_key` IS NULL;

-- تحديث اللغة الافتراضية للضيوف
UPDATE `guests` SET `current_language_key` = 'ar' WHERE `current_language_key` = 'en' OR `current_language_key` IS NULL;

-- تحديث إعدادات العملة لتتناسب مع اللغة العربية
UPDATE `business_settings` 
SET `live_values` = JSON_SET(`live_values`, '$.currency_symbol_position', 'right')
WHERE `key_name` = 'business_information' AND `settings_type` = 'business_information';

-- إضافة ترجمات عربية لبعض الإعدادات المهمة
-- يمكن إضافة المزيد حسب الحاجة

-- تحديث إعدادات إضافية لتحسين تجربة اللغة العربية

-- تحديث اسم التطبيق ليكون بالعربية (اختياري)
UPDATE `business_settings`
SET `live_values` = JSON_SET(`live_values`, '$.business_name', 'نظام الخدمات')
WHERE `key_name` = 'business_information' AND `settings_type` = 'business_information';

-- تحديث وصف التطبيق (اختياري)
UPDATE `business_settings`
SET `live_values` = JSON_SET(`live_values`, '$.business_short_description', 'منصة شاملة لإدارة الخدمات')
WHERE `key_name` = 'business_information' AND `settings_type` = 'business_information';

-- تحديث إعدادات التاريخ والوقت لتناسب المنطقة العربية
UPDATE `business_settings`
SET `live_values` = JSON_SET(`live_values`, '$.time_zone', 'Asia/Riyadh')
WHERE `key_name` = 'business_information' AND `settings_type` = 'business_information';

-- تحديث إعدادات العملة للريال السعودي (اختياري)
UPDATE `business_settings`
SET `live_values` = JSON_SET(
    `live_values`,
    '$.currency_symbol', 'ر.س',
    '$.currency_code', 'SAR',
    '$.currency_symbol_position', 'right'
)
WHERE `key_name` = 'business_information' AND `settings_type` = 'business_information';

-- إضافة ترجمات عربية لبعض الرسائل المهمة
INSERT IGNORE INTO `translations` (`translationable_type`, `translationable_id`, `locale`, `key`, `value`)
SELECT
    'Modules\\BusinessSettingsModule\\Entities\\BusinessSettings',
    bs.id,
    'ar',
    'welcome_message',
    'مرحباً بك في نظام إدارة الخدمات'
FROM `business_settings` bs
WHERE bs.key_name = 'business_information' AND bs.settings_type = 'business_information';

-- رسالة تأكيد
SELECT 'تم تطبيق إعدادات اللغة العربية بنجاح!' as 'Status';
SELECT 'Arabic language settings applied successfully!' as 'Status_EN';
SELECT 'يرجى تشغيل الأوامر التالية في Terminal:' as 'Next_Steps';
SELECT 'php artisan cache:clear && php artisan config:clear && php artisan view:clear' as 'Commands';

-- ملاحظات مهمة:
-- 1. تأكد من وجود ملف resources/lang/ar/lang.php
-- 2. قم بمسح الكاش بعد تشغيل هذا الملف: php artisan cache:clear
-- 3. قم بتحديث الصفحة في المتصفح
-- 4. تأكد من أن CSS يدعم RTL للعربية
-- 5. يمكنك تعديل اسم التطبيق والعملة حسب احتياجاتك

-- أوامر إضافية لمسح الكاش (يجب تشغيلها من terminal)
-- php artisan cache:clear
-- php artisan config:clear
-- php artisan view:clear
-- php artisan route:clear
-- php artisan optimize:clear
