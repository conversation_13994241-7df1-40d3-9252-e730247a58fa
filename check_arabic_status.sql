-- ملف للتحقق من حالة اللغة العربية في النظام
-- Arabic Language Status Check SQL File

-- التحقق من إعدادات اللغة الحالية
SELECT 
    'إعدادات اللغة الحالية' as 'Check_Type',
    key_name,
    live_values,
    settings_type,
    is_active
FROM business_settings 
WHERE key_name = 'system_language' AND settings_type = 'business_information';

-- التحقق من لغة المستخدمين
SELECT 
    'توزيع لغات المستخدمين' as 'Check_Type',
    current_language_key as 'Language',
    COUNT(*) as 'User_Count'
FROM users 
GROUP BY current_language_key;

-- التحقق من لغة الضيوف
SELECT 
    'توزيع لغات الضيوف' as 'Check_Type',
    current_language_key as 'Language',
    COUNT(*) as 'Guest_Count'
FROM guests 
GROUP BY current_language_key;

-- التحقق من إعدادات العمل الأساسية
SELECT 
    'إعدادات العمل' as 'Check_Type',
    key_name,
    JSON_EXTRACT(live_values, '$.business_name') as 'Business_Name',
    JSON_EXTRACT(live_values, '$.currency_symbol') as 'Currency_Symbol',
    JSON_EXTRACT(live_values, '$.currency_code') as 'Currency_Code',
    JSON_EXTRACT(live_values, '$.time_zone') as 'Time_Zone'
FROM business_settings 
WHERE key_name = 'business_information' AND settings_type = 'business_information';

-- التحقق من وجود ترجمات عربية
SELECT 
    'الترجمات العربية' as 'Check_Type',
    COUNT(*) as 'Arabic_Translations_Count'
FROM translations 
WHERE locale = 'ar';

-- التحقق من حالة الجداول المطلوبة
SELECT 
    'حالة الجداول' as 'Check_Type',
    'business_settings' as 'Table_Name',
    COUNT(*) as 'Records_Count'
FROM business_settings
UNION ALL
SELECT 
    'حالة الجداول' as 'Check_Type',
    'users' as 'Table_Name',
    COUNT(*) as 'Records_Count'
FROM users
UNION ALL
SELECT 
    'حالة الجداول' as 'Check_Type',
    'translations' as 'Table_Name',
    COUNT(*) as 'Records_Count'
FROM translations;

-- فحص شامل لحالة اللغة العربية
SELECT 
    CASE 
        WHEN (
            SELECT JSON_EXTRACT(live_values, '$[*].code') 
            FROM business_settings 
            WHERE key_name = 'system_language'
        ) LIKE '%"ar"%' THEN 'العربية مُفعلة ✅'
        ELSE 'العربية غير مُفعلة ❌'
    END as 'Arabic_Status',
    
    CASE 
        WHEN (
            SELECT JSON_EXTRACT(live_values, '$[*].default') 
            FROM business_settings 
            WHERE key_name = 'system_language'
        ) LIKE '%true%' AND (
            SELECT JSON_EXTRACT(live_values, '$[*].code') 
            FROM business_settings 
            WHERE key_name = 'system_language'
        ) LIKE '%"ar"%' THEN 'العربية هي اللغة الافتراضية ✅'
        ELSE 'العربية ليست اللغة الافتراضية ⚠️'
    END as 'Default_Language_Status',
    
    CONCAT(
        (SELECT COUNT(*) FROM users WHERE current_language_key = 'ar'),
        ' من أصل ',
        (SELECT COUNT(*) FROM users),
        ' مستخدم يستخدم العربية'
    ) as 'Users_Arabic_Usage';

-- رسائل الحالة النهائية
SELECT 
    '=== تقرير حالة اللغة العربية ===' as 'Final_Report';

SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM business_settings 
            WHERE key_name = 'system_language' 
            AND JSON_EXTRACT(live_values, '$[*].code') LIKE '%"ar"%'
        ) THEN '✅ اللغة العربية مُثبتة في النظام'
        ELSE '❌ اللغة العربية غير مُثبتة'
    END as 'Installation_Status';

SELECT 
    CASE 
        WHEN (
            SELECT COUNT(*) FROM users WHERE current_language_key = 'ar'
        ) > 0 THEN '✅ يوجد مستخدمون يستخدمون العربية'
        ELSE '⚠️ لا يوجد مستخدمون يستخدمون العربية'
    END as 'Usage_Status';

-- تعليمات الإصلاح إذا لزم الأمر
SELECT 
    'إذا كانت النتائج غير مرضية، قم بتشغيل ملف arabic_language_setup.sql' as 'Fix_Instructions';
