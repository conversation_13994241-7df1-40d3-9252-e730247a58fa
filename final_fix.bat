@echo off
chcp 65001 >nul
echo ===============================================
echo        الإصلاح النهائي للموقع
echo         Final Website Fix
echo ===============================================
echo.

echo 🔧 تطبيق الإصلاح النهائي...
echo.

echo 1️⃣ تنظيف شامل للكاش...
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan optimize:clear

echo.
echo 2️⃣ إنشاء مفتاح تطبيق جديد...
php artisan key:generate --force

echo.
echo 3️⃣ تشغيل migrations...
php artisan migrate --force

echo.
echo 4️⃣ إنشاء رابط التخزين...
php artisan storage:link

echo.
echo 5️⃣ إعداد الصلاحيات...
if exist storage (
    echo إعداد صلاحيات storage...
    attrib -r storage\* /s /d
)

if exist bootstrap\cache (
    echo إعداد صلاحيات bootstrap\cache...
    attrib -r bootstrap\cache\* /s /d
)

echo.
echo 6️⃣ تشغيل seeders الأساسية...
php artisan db:seed --class=AdminTableSeeder --force
php artisan db:seed --class=BusinessSettingsTableSeeder --force

echo.
echo 7️⃣ تحسين الأداء...
php artisan config:cache
php artisan route:cache

echo.
echo 8️⃣ اختبار الاتصال...
php artisan tinker --execute="try { DB::connection()->getPdo(); echo 'قاعدة البيانات متصلة!'; } catch(Exception $e) { echo 'خطأ: ' . $e->getMessage(); }"

echo.
echo ===============================================
echo ✅ تم الانتهاء من الإصلاح!
echo.
echo الآن جرب الموقع:
echo https://www.zainalabidin.pro
echo.
echo إذا استمر الخطأ:
echo 1. تحقق من إعدادات قاعدة البيانات في cPanel
echo 2. تأكد من أن قاعدة البيانات موجودة
echo 3. تأكد من صحة اسم المستخدم وكلمة المرور
echo ===============================================
pause
