@echo off
chcp 65001 >nul
echo ===============================================
echo        تشغيل تطبيق الهاتف
echo         Run Mobile App
echo ===============================================
echo.

echo 📱 تطبيق مربوط بالموقع: https://www.zainalabidin.pro
echo 📂 مجلد التطبيق: التطبيق\User app and web
echo.

echo 1️⃣ التحقق من Flutter...
flutter --version
if %errorlevel% neq 0 (
    echo ❌ Flutter غير مثبت!
    echo يرجى تثبيت Flutter من: https://flutter.dev
    pause
    exit /b 1
)

echo.
echo 2️⃣ الانتقال إلى مجلد التطبيق...
cd "التطبيق\User app and web"

echo.
echo 3️⃣ تحديث dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ فشل في تحديث dependencies
    pause
    exit /b 1
)

echo.
echo 4️⃣ فحص الأجهزة المتصلة...
flutter devices

echo.
echo 5️⃣ تنظيف التطبيق...
flutter clean
flutter pub get

echo.
echo ===============================================
echo 🚀 خيارات التشغيل:
echo.
echo 1. تشغيل على المحاكي/الجهاز المتصل
echo 2. بناء APK للاختبار
echo 3. بناء APK للإنتاج
echo 4. تشغيل على الويب
echo 5. إلغاء
echo.
set /p choice="اختر رقم (1-5): "

if "%choice%"=="1" (
    echo.
    echo 🔄 تشغيل التطبيق...
    flutter run
) else if "%choice%"=="2" (
    echo.
    echo 🔨 بناء APK للاختبار...
    flutter build apk --debug
    echo ✅ تم إنشاء APK في: build\app\outputs\flutter-apk\app-debug.apk
) else if "%choice%"=="3" (
    echo.
    echo 🔨 بناء APK للإنتاج...
    flutter build apk --release
    echo ✅ تم إنشاء APK في: build\app\outputs\flutter-apk\app-release.apk
) else if "%choice%"=="4" (
    echo.
    echo 🌐 تشغيل على الويب...
    flutter run -d chrome
) else (
    echo إلغاء العملية...
    goto end
)

echo.
echo ===============================================
echo ✅ تم الانتهاء!
echo.
echo 📱 التطبيق متصل بالموقع: https://www.zainalabidin.pro
echo 🎛️ لوحة التحكم: https://www.zainalabidin.pro/admin
echo.
echo يمكنك الآن:
echo - إدارة التطبيق من لوحة التحكم
echo - إضافة الخدمات والفئات
echo - إدارة المستخدمين
echo - تخصيص الإعدادات
echo ===============================================

:end
pause
