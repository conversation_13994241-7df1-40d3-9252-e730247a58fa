<?php
/**
 * اختبار الاتصال بقاعدة البيانات
 * Database Connection Test
 */

echo "===============================================\n";
echo "        اختبار الاتصال بقاعدة البيانات\n";
echo "       Database Connection Test\n";
echo "===============================================\n\n";

// معلومات قاعدة البيانات
$host = 'localhost';
$database = 'kidzrcle_kalfa';
$username = 'kidzrcle_kalfa';
$password = 'kidzrcle_kalfa';

echo "🔍 معلومات الاتصال:\n";
echo "Host: $host\n";
echo "Database: $database\n";
echo "Username: $username\n";
echo "Password: " . str_repeat('*', strlen($password)) . "\n\n";

// اختبار الاتصال المباشر
echo "1️⃣ اختبار الاتصال المباشر...\n";
try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ الاتصال المباشر ناجح!\n\n";
    
    // فحص الجداول
    echo "2️⃣ فحص الجداول الموجودة...\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (count($tables) > 0) {
        echo "✅ عدد الجداول: " . count($tables) . "\n";
        echo "أول 10 جداول:\n";
        foreach (array_slice($tables, 0, 10) as $table) {
            echo "  - $table\n";
        }
    } else {
        echo "⚠️ لا توجد جداول - ستحتاج لتشغيل migrations\n";
    }
    
    echo "\n";
    
    // فحص جدول business_settings
    echo "3️⃣ فحص جدول business_settings...\n";
    if (in_array('business_settings', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM business_settings");
        $count = $stmt->fetchColumn();
        echo "✅ جدول business_settings موجود مع $count سجل\n";
    } else {
        echo "⚠️ جدول business_settings غير موجود\n";
    }
    
} catch (PDOException $e) {
    echo "❌ فشل الاتصال: " . $e->getMessage() . "\n";
    echo "\nتحقق من:\n";
    echo "1. صحة معلومات قاعدة البيانات\n";
    echo "2. أن قاعدة البيانات موجودة\n";
    echo "3. أن المستخدم له صلاحيات الوصول\n";
}

echo "\n";

// اختبار Laravel
echo "4️⃣ اختبار Laravel...\n";
try {
    if (file_exists('vendor/autoload.php')) {
        require_once 'vendor/autoload.php';
        
        if (file_exists('bootstrap/app.php')) {
            $app = require_once 'bootstrap/app.php';
            $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
            $kernel->bootstrap();
            
            // اختبار الاتصال
            $pdo = DB::connection()->getPdo();
            echo "✅ Laravel يتصل بقاعدة البيانات بنجاح!\n";
            
            // فحص إعدادات العمل
            $settings = DB::table('business_settings')->count();
            echo "✅ عدد إعدادات العمل: $settings\n";
            
        } else {
            echo "❌ ملف bootstrap/app.php مفقود\n";
        }
    } else {
        echo "❌ Composer autoload مفقود - شغل: composer install\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في Laravel: " . $e->getMessage() . "\n";
}

echo "\n===============================================\n";
echo "✅ انتهى اختبار قاعدة البيانات\n";
echo "===============================================\n";

// تعليمات الخطوات التالية
echo "\n🚀 الخطوات التالية:\n";
echo "1. إذا كان الاتصال ناجح، شغل: fix_with_correct_database.bat\n";
echo "2. إذا فشل الاتصال، تحقق من معلومات قاعدة البيانات في cPanel\n";
echo "3. بعد الإصلاح، جرب الموقع: https://www.zainalabidin.pro\n";
?>
