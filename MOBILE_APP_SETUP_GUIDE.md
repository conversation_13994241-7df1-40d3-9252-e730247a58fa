# دليل ربط تطبيق الهاتف بالموقع
# Mobile App Setup Guide

## 📱 نظرة عامة

لديك تطبيق Flutter يحتاج إلى ربطه بموقع Laravel الخاص بك. التطبيق موجود في مجلد `التطبيق/User app and web`.

## 🔧 الملفات التي تحتاج تعديل

### 1. ملف إعدادات التطبيق الرئيسي
**المسار:** `التطبيق/User app and web/lib/utils/app_constants.dart`

#### التعديل المطلوب:
```dart
// السطر 8 - غير هذا
static const String baseUrl = 'YOUR_BASE_URL';

// إلى هذا (ضع رابط موقعك)
static const String baseUrl = 'https://yourdomain.com';
```

### 2. ملف Android Manifest
**المسار:** `التطبيق/User app and web/android/app/src/main/AndroidManifest.xml`

#### التعديل المطلوب:
```xml
<!-- السطر 36 - غير هذا -->
<meta-data android:name="com.google.android.geo.API_KEY"
    android:value="YOUR_MAP_KEY"/>

<!-- إلى هذا (ضع مفتاح Google Maps) -->
<meta-data android:name="com.google.android.geo.API_KEY"
    android:value="YOUR_ACTUAL_GOOGLE_MAPS_API_KEY"/>
```

## 📋 خطوات الإعداد التفصيلية

### الخطوة 1: تحديث رابط الموقع

1. افتح ملف `app_constants.dart`
2. ابحث عن السطر: `static const String baseUrl = 'YOUR_BASE_URL';`
3. غيره إلى: `static const String baseUrl = 'https://yourdomain.com';`
4. تأكد من عدم وجود `/` في نهاية الرابط

### الخطوة 2: إعداد Google Maps (اختياري)

1. احصل على Google Maps API Key من [Google Cloud Console](https://console.cloud.google.com/)
2. فعل الخدمات التالية:
   - Maps SDK for Android
   - Maps SDK for iOS
   - Places API
   - Geocoding API

3. ضع المفتاح في ملف AndroidManifest.xml

### الخطوة 3: إعداد Firebase (للإشعارات)

1. أنشئ مشروع Firebase جديد
2. أضف التطبيق للمشروع
3. حمل ملف `google-services.json`
4. ضعه في `android/app/`

### الخطوة 4: تحديث إعدادات الشبكة

تأكد من أن موقعك يدعم:
- **HTTPS** (مطلوب للتطبيق)
- **CORS** مُفعل للدومين
- **API endpoints** تعمل بشكل صحيح

## 🌐 إعدادات الموقع المطلوبة

### 1. تفعيل CORS

أضف هذا إلى ملف `config/cors.php` في موقعك:

```php
'paths' => ['api/*', 'sanctum/csrf-cookie'],
'allowed_methods' => ['*'],
'allowed_origins' => ['*'], // أو ضع دومين التطبيق
'allowed_origins_patterns' => [],
'allowed_headers' => ['*'],
'exposed_headers' => [],
'max_age' => 0,
'supports_credentials' => false,
```

### 2. تحديث ملف .env في الموقع

```env
APP_URL=https://yourdomain.com
SANCTUM_STATEFUL_DOMAINS=yourdomain.com
SESSION_DOMAIN=yourdomain.com
```

### 3. إعداد API Routes

تأكد من أن جميع routes في `routes/api.php` تعمل وتبدأ بـ `/api/v1/`

## 📱 بناء التطبيق

### للتطوير والاختبار:
```bash
cd "التطبيق/User app and web"
flutter pub get
flutter run
```

### لبناء APK للإنتاج:
```bash
flutter build apk --release
```

### لبناء App Bundle:
```bash
flutter build appbundle --release
```

## 🔍 اختبار الاتصال

### 1. اختبار API من المتصفح:
```
https://yourdomain.com/api/v1/customer/config
```

### 2. اختبار من التطبيق:
- شغل التطبيق في وضع debug
- راقب console للأخطاء
- اختبر تسجيل الدخول والتسجيل

## 🚨 مشاكل شائعة وحلولها

### 1. خطأ "Network Error":
- تأكد من أن الموقع يعمل
- تحقق من إعدادات CORS
- تأكد من أن HTTPS مُفعل

### 2. خطأ "Unauthorized":
- تحقق من إعدادات Sanctum
- تأكد من صحة API tokens

### 3. مشاكل Google Maps:
- تأكد من صحة API Key
- تحقق من تفعيل الخدمات المطلوبة
- تأكد من إعدادات billing في Google Cloud

### 4. مشاكل Firebase:
- تأكد من وضع google-services.json في المكان الصحيح
- تحقق من إعدادات package name

## 📁 هيكل الملفات المهمة

```
التطبيق/User app and web/
├── lib/
│   ├── utils/
│   │   └── app_constants.dart     ← الملف الأهم للتعديل
│   ├── api/
│   │   └── remote/               ← ملفات API calls
│   └── main.dart
├── android/
│   └── app/
│       ├── src/main/AndroidManifest.xml  ← إعدادات Android
│       └── google-services.json          ← ملف Firebase
├── ios/
│   └── Runner/
│       └── Info.plist            ← إعدادات iOS
└── pubspec.yaml                  ← dependencies
```

## ✅ قائمة التحقق النهائية

- [ ] تم تحديث baseUrl في app_constants.dart
- [ ] تم إعداد Google Maps API Key
- [ ] تم إعداد Firebase (إذا كان مطلوباً)
- [ ] الموقع يعمل على HTTPS
- [ ] CORS مُفعل في الموقع
- [ ] API endpoints تعمل بشكل صحيح
- [ ] تم اختبار التطبيق والاتصال

## 🎯 الخطوة التالية

بعد إكمال هذه الخطوات، ستحتاج إلى:
1. بناء التطبيق للإنتاج
2. رفعه على Google Play Store أو App Store
3. اختبار جميع الوظائف

تم إنشاء هذا الدليل لمساعدتك في ربط التطبيق بالموقع بنجاح! 🚀
