@echo off
chcp 65001 >nul
echo ===============================================
echo        بناء تطبيق APK
echo         Build APK
echo ===============================================
echo.

echo 📱 بناء تطبيق زين العابدين
echo 🌐 متصل بالموقع: https://www.zainalabidin.pro
echo.

echo الانتقال إلى مجلد التطبيق...
cd "التطبيق\User app and web"

echo.
echo 🧹 تنظيف المشروع...
flutter clean

echo.
echo 📦 تحديث dependencies...
flutter pub get

echo.
echo ===============================================
echo اختر نوع APK المطلوب:
echo.
echo 1. Debug APK (للاختبار - حجم أكبر)
echo 2. Release APK (للنشر - حجم أصغر ومحسن)
echo 3. App Bundle (للنشر على Google Play)
echo 4. إلغاء
echo.
set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🔨 بناء Debug APK...
    flutter build apk --debug
    set "apk_path=build\app\outputs\flutter-apk\app-debug.apk"
    set "apk_type=Debug"
) else if "%choice%"=="2" (
    echo.
    echo 🔨 بناء Release APK...
    flutter build apk --release
    set "apk_path=build\app\outputs\flutter-apk\app-release.apk"
    set "apk_type=Release"
) else if "%choice%"=="3" (
    echo.
    echo 🔨 بناء App Bundle...
    flutter build appbundle --release
    set "apk_path=build\app\outputs\bundle\release\app-release.aab"
    set "apk_type=App Bundle"
) else (
    echo إلغاء العملية...
    goto end
)

echo.
echo ===============================================
echo ✅ تم بناء %apk_type% بنجاح!
echo.
echo 📁 مكان الملف:
echo %apk_path%
echo.

REM فحص وجود الملف
if exist "%apk_path%" (
    echo ✅ الملف موجود
    
    REM عرض حجم الملف
    for %%A in ("%apk_path%") do (
        set "file_size=%%~zA"
        set /a "file_size_mb=!file_size! / 1024 / 1024"
        echo 📏 حجم الملف: !file_size_mb! MB
    )
    
    echo.
    echo 🚀 يمكنك الآن:
    echo 1. تثبيت APK على الهاتف للاختبار
    echo 2. رفع APK/Bundle على Google Play Store
    echo 3. مشاركة APK مع المستخدمين
    
    echo.
    echo 📱 لتثبيت APK على الهاتف:
    echo 1. انسخ الملف إلى الهاتف
    echo 2. فعل "Unknown Sources" في إعدادات الأمان
    echo 3. اضغط على الملف لتثبيته
    
    echo.
    set /p open_folder="هل تريد فتح مجلد الملف؟ (y/n): "
    if /i "%open_folder%"=="y" (
        explorer "build\app\outputs\flutter-apk"
    )
    
) else (
    echo ❌ فشل في إنشاء الملف
    echo تحقق من الأخطاء أعلاه
)

echo.
echo ===============================================
echo 📋 معلومات التطبيق:
echo.
echo اسم التطبيق: Demandium
echo الموقع المربوط: https://www.zainalabidin.pro
echo إصدار Flutter: 
flutter --version | findstr "Flutter"
echo.
echo 🎯 التطبيق جاهز للاستخدام!
echo ===============================================

:end
pause
