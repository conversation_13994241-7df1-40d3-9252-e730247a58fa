#!/bin/bash

echo "==============================================="
echo "        إعداد التطبيق للموقع"
echo "        Domain Setup Script"
echo "==============================================="
echo ""

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة لطباعة رسائل ملونة
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# التحقق من وجود PHP
if ! command -v php &> /dev/null; then
    print_error "PHP غير مثبت على الخادم"
    exit 1
fi

print_status "PHP موجود: $(php -v | head -n1)"

# التحقق من وجود Composer
if ! command -v composer &> /dev/null; then
    print_warning "Composer غير مثبت، سيتم تثبيته..."
    curl -sS https://getcomposer.org/installer | php
    sudo mv composer.phar /usr/local/bin/composer
    print_status "تم تثبيت Composer"
fi

# إنشاء ملف .env إذا لم يكن موجوداً
if [ ! -f .env ]; then
    print_info "إنشاء ملف .env..."
    cp .env.example .env
    print_status "تم إنشاء ملف .env"
else
    print_warning "ملف .env موجود بالفعل"
fi

# طلب معلومات الدومين
echo ""
print_info "يرجى إدخال معلومات الدومين:"
read -p "أدخل اسم الدومين (مثال: https://yourdomain.com): " domain_url
read -p "أدخل اسم قاعدة البيانات: " db_name
read -p "أدخل اسم مستخدم قاعدة البيانات: " db_user
read -s -p "أدخل كلمة مرور قاعدة البيانات: " db_password
echo ""

# تحديث ملف .env
print_info "تحديث ملف .env..."
sed -i "s|APP_URL=.*|APP_URL=$domain_url|g" .env
sed -i "s|DB_DATABASE=.*|DB_DATABASE=$db_name|g" .env
sed -i "s|DB_USERNAME=.*|DB_USERNAME=$db_user|g" .env
sed -i "s|DB_PASSWORD=.*|DB_PASSWORD=$db_password|g" .env
sed -i "s|APP_ENV=.*|APP_ENV=production|g" .env
sed -i "s|APP_DEBUG=.*|APP_DEBUG=false|g" .env

print_status "تم تحديث ملف .env"

# تثبيت التبعيات
print_info "تثبيت تبعيات Composer..."
composer install --optimize-autoloader --no-dev
print_status "تم تثبيت التبعيات"

# إنشاء مفتاح التطبيق
print_info "إنشاء مفتاح التطبيق..."
php artisan key:generate --force
print_status "تم إنشاء مفتاح التطبيق"

# إعداد الصلاحيات
print_info "إعداد صلاحيات المجلدات..."
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
print_status "تم إعداد الصلاحيات"

# إنشاء symbolic link للتخزين
print_info "إنشاء رابط التخزين..."
php artisan storage:link
print_status "تم إنشاء رابط التخزين"

# تحسين الأداء
print_info "تحسين أداء التطبيق..."
php artisan config:cache
php artisan route:cache
php artisan view:cache
print_status "تم تحسين الأداء"

# اختبار الاتصال بقاعدة البيانات
print_info "اختبار الاتصال بقاعدة البيانات..."
if php artisan tinker --execute="DB::connection()->getPdo(); echo 'اتصال ناجح';" 2>/dev/null; then
    print_status "الاتصال بقاعدة البيانات ناجح"
else
    print_error "فشل الاتصال بقاعدة البيانات - تحقق من الإعدادات"
fi

# إنشاء ملف .htaccess للـ Apache
if [ ! -f .htaccess ]; then
    print_info "إنشاء ملف .htaccess..."
    cat > .htaccess << 'EOF'
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteRule ^(.*)$ public/$1 [L]
</IfModule>
EOF
    print_status "تم إنشاء ملف .htaccess"
fi

echo ""
echo "==============================================="
print_status "تم إعداد التطبيق بنجاح!"
echo ""
print_info "الخطوات التالية:"
echo "1. تأكد من إعداد DNS للدومين"
echo "2. قم بتشغيل: php artisan migrate (إذا لم تكن قد فعلت ذلك)"
echo "3. اختبر الموقع على: $domain_url"
echo "4. قم بإعداد SSL Certificate للأمان"
echo ""
print_warning "لا تنس تشغيل arabic_language_setup.sql لتفعيل اللغة العربية"
echo "==============================================="
