-- مل<PERSON> إعداد Google Maps API
-- Google Maps API Setup SQL File

-- إدراج أو تحديث إعدادات Google Maps
INSERT INTO `business_settings` (`id`, `key_name`, `live_values`, `test_values`, `settings_type`, `mode`, `is_active`, `created_at`, `updated_at`) 
VALUES (
    UUID(),
    'google_map',
    '{
        "map_api_key_server": "AIzaSyDummy_Server_Key_Replace_With_Real_Key",
        "map_api_key_client": "AIzaSyDummy_Client_Key_Replace_With_Real_Key"
    }',
    '{
        "map_api_key_server": "AIzaSyDummy_Server_Key_Replace_With_Real_Key",
        "map_api_key_client": "AIzaSyDummy_Client_Key_Replace_With_Real_Key"
    }',
    'third_party',
    'live',
    1,
    NOW(),
    NOW()
)
ON DUPLICATE KEY UPDATE
    `live_values` = VALUES(`live_values`),
    `test_values` = VALUES(`test_values`),
    `updated_at` = NOW();

-- رسالة تأكيد
SELECT 'تم إعداد Google Maps API بنجاح!' as 'Status';
SELECT 'Google Maps API setup completed!' as 'Status_EN';

-- تعليمات مهمة
SELECT 'يرجى الحصول على Google Maps API Key من Google Cloud Console' as 'Instructions';
SELECT 'ثم تحديث المفاتيح في لوحة التحكم' as 'Next_Step';

-- معلومات الوصول
SELECT 'اذهب إلى: Settings > Third Party > Google Map' as 'Admin_Panel_Path';
SELECT 'أو: https://www.zainalabidin.pro/admin/configuration/get-third-party-config?web_page=google_map' as 'Direct_Link';
