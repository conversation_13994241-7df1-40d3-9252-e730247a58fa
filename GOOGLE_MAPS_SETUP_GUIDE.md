# دليل إعداد Google Maps API
# Google Maps API Setup Guide

## 🗺️ المشكلة الحالية:
عند إضافة مزود خدمة، تظهر رسالة خطأ: "لم تحمِّل هذه الصفحة خرائط Google بشكل صحيح"

## 🔧 السبب:
Google Maps API Key غير مُعد أو غير صحيح

---

## 📋 الحل الكامل:

### الخطوة 1: الحصول على Google Maps API Key

#### 1.1 اذهب إلى Google Cloud Console:
```
https://console.cloud.google.com/
```

#### 1.2 إنشاء مشروع جديد (إذا لم يكن لديك):
1. اضغط على "Select a project"
2. اضغط "NEW PROJECT"
3. أدخل اسم المشروع: "Zain Alabidin Services"
4. اضغط "CREATE"

#### 1.3 تفعيل APIs المطلوبة:
1. اذهب إلى "APIs & Services" > "Library"
2. ابحث عن وفعل هذه APIs:
   - ✅ **Maps JavaScript API**
   - ✅ **Places API**
   - ✅ **Geocoding API**
   - ✅ **Maps SDK for Android** (للتطبيق)

#### 1.4 إنشاء API Keys:
1. اذهب إلى "APIs & Services" > "Credentials"
2. اضغط "CREATE CREDENTIALS" > "API key"
3. ستحصل على مفتاح مثل: `AIzaSyC4YjJ9_example_key_here`

#### 1.5 تقييد المفاتيح (مُنصح به):

**للمفتاح الأول (Client Key):**
- اضغط على المفتاح لتعديله
- في "Application restrictions" اختر "HTTP referrers"
- أضف هذه المواقع:
  ```
  https://www.zainalabidin.pro/*
  https://zainalabidin.pro/*
  http://localhost/*
  ```

**للمفتاح الثاني (Server Key):**
- اضغط على المفتاح لتعديله
- في "Application restrictions" اختر "IP addresses"
- أضف IP الخادم الخاص بك

---

### الخطوة 2: إعداد المفاتيح في الموقع

#### 2.1 تشغيل ملف SQL:
```sql
-- شغل هذا الملف في phpMyAdmin
setup_google_maps.sql
```

#### 2.2 تحديث المفاتيح في لوحة التحكم:
1. **اذهب إلى لوحة التحكم:**
   ```
   https://www.zainalabidin.pro/admin
   ```

2. **اذهب إلى الإعدادات:**
   ```
   Settings > Third Party > Google Map
   ```

3. **أو استخدم الرابط المباشر:**
   ```
   https://www.zainalabidin.pro/admin/configuration/get-third-party-config?web_page=google_map
   ```

4. **أدخل المفاتيح:**
   - **Map API Key Server:** `AIzaSyC4YjJ9_your_server_key_here`
   - **Map API Key Client:** `AIzaSyC4YjJ9_your_client_key_here`

5. **اضغط "Update"**

---

### الخطوة 3: مسح الكاش

#### شغل هذه الأوامر:
```bash
php artisan cache:clear
php artisan config:clear
php artisan view:clear
```

---

### الخطوة 4: اختبار الخرائط

#### 4.1 اختبر في الموقع:
1. اذهب إلى صفحة تسجيل مزود الخدمة:
   ```
   https://www.zainalabidin.pro/provider/auth/sign-up
   ```

2. جرب تحديد الموقع

#### 4.2 اختبر في لوحة التحكم:
1. اذهب إلى إضافة مزود خدمة جديد
2. جرب تحديد الموقع على الخريطة

---

## 🚨 حلول للمشاكل الشائعة:

### مشكلة: "This page can't load Google Maps correctly"
**الحل:**
- تأكد من تفعيل Maps JavaScript API
- تحقق من صحة API Key
- تأكد من عدم تجاوز الحد المسموح

### مشكلة: "RefererNotAllowedMapError"
**الحل:**
- أضف النطاق الصحيح في HTTP referrers
- تأكد من إضافة `https://www.zainalabidin.pro/*`

### مشكلة: "ApiNotActivatedMapError"
**الحل:**
- تأكد من تفعيل جميع APIs المطلوبة
- انتظر بضع دقائق بعد التفعيل

### مشكلة: "OverQueryLimitError"
**الحل:**
- تحقق من استخدام API في Google Cloud Console
- قد تحتاج لتفعيل الفوترة للاستخدام المكثف

---

## 💰 معلومات التكلفة:

### الاستخدام المجاني:
- **Maps JavaScript API:** 28,000 طلب شهرياً مجاناً
- **Places API:** 17,000 طلب شهرياً مجاناً
- **Geocoding API:** 40,000 طلب شهرياً مجاناً

### للاستخدام التجاري:
- قد تحتاج لتفعيل الفوترة
- التكلفة منخفضة للمواقع الصغيرة والمتوسطة

---

## 🎯 الخطوات السريعة:

### إذا كنت تريد الحل السريع:

1. **احصل على API Key من:**
   ```
   https://console.cloud.google.com/
   ```

2. **شغل ملف SQL:**
   ```sql
   setup_google_maps.sql
   ```

3. **أدخل المفتاح في لوحة التحكم:**
   ```
   Settings > Third Party > Google Map
   ```

4. **امسح الكاش:**
   ```bash
   php artisan cache:clear
   ```

5. **اختبر الخرائط!**

---

## 📞 إذا احتجت مساعدة:

### أخبرني:
- هل حصلت على API Key؟
- ما هي رسالة الخطأ الحالية؟
- هل فعلت APIs المطلوبة؟

**الهدف: جعل الخرائط تعمل بشكل صحيح في الموقع والتطبيق!** 🎯

---

## ✅ قائمة التحقق:

- [ ] إنشاء مشروع في Google Cloud Console
- [ ] تفعيل Maps JavaScript API
- [ ] تفعيل Places API
- [ ] تفعيل Geocoding API
- [ ] إنشاء API Keys
- [ ] تقييد المفاتيح (اختياري)
- [ ] تشغيل setup_google_maps.sql
- [ ] إدخال المفاتيح في لوحة التحكم
- [ ] مسح الكاش
- [ ] اختبار الخرائط

**بمجرد إكمال هذه الخطوات، ستعمل الخرائط بشكل مثالي!** 🗺️
