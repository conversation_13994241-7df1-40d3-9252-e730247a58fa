@echo off
chcp 65001 >nul
echo ===============================================
echo        تثبيت Flutter
echo         Install Flutter
echo ===============================================
echo.

echo 📱 تثبيت Flutter لبناء التطبيق...
echo.

echo 1️⃣ فحص وجود Flutter...
flutter --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Flutter مثبت بالفعل!
    flutter --version
    goto build_app
)

echo ❌ Flutter غير مثبت
echo.

echo 2️⃣ إنشاء مجلد Flutter...
if not exist "C:\flutter" (
    mkdir "C:\flutter"
    echo ✅ تم إنشاء مجلد C:\flutter
)

echo.
echo 3️⃣ تحميل Flutter...
echo 📥 يرجى تحميل Flutter يدوياً من:
echo https://docs.flutter.dev/get-started/install/windows
echo.
echo أو استخدم هذا الرابط المباشر:
echo https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.24.5-stable.zip
echo.

echo 📋 خطوات التثبيت اليدوي:
echo 1. حمل ملف ZIP من الرابط أعلاه
echo 2. استخرج الملفات إلى C:\flutter
echo 3. أضف C:\flutter\bin إلى PATH
echo 4. أعد تشغيل Command Prompt
echo 5. شغل: flutter doctor
echo.

echo 4️⃣ إضافة Flutter إلى PATH...
echo.
echo اتبع هذه الخطوات لإضافة Flutter إلى PATH:
echo.
echo 1. اضغط Win + R واكتب: sysdm.cpl
echo 2. اذهب إلى تبويب "Advanced"
echo 3. اضغط "Environment Variables"
echo 4. في "System Variables" ابحث عن "Path"
echo 5. اضغط "Edit" ثم "New"
echo 6. أضف: C:\flutter\bin
echo 7. اضغط OK على جميع النوافذ
echo 8. أعد تشغيل Command Prompt
echo.

echo 5️⃣ بدائل أخرى...
echo.
echo البديل 1: استخدام Chocolatey
echo choco install flutter
echo.
echo البديل 2: استخدام Scoop
echo scoop bucket add extras
echo scoop install flutter
echo.

echo ===============================================
echo ⚠️ بعد تثبيت Flutter:
echo.
echo 1. أعد تشغيل Command Prompt
echo 2. شغل: flutter doctor
echo 3. ثبت Android Studio إذا لم يكن مثبت
echo 4. شغل: build_apk.bat
echo.
echo ===============================================

:build_app
echo.
echo هل تريد المتابعة لبناء التطبيق؟ (إذا كان Flutter مثبت)
set /p continue="اضغط Y للمتابعة أو أي مفتاح للخروج: "
if /i "%continue%"=="y" (
    echo.
    echo 🚀 بناء التطبيق...
    cd "User app and web"
    flutter clean
    flutter pub get
    flutter build apk --release
    echo.
    echo ✅ تم بناء التطبيق!
    echo 📁 الملف في: build\app\outputs\flutter-apk\app-release.apk
)

pause
