<?php
/**
 * ملف إعداد CORS للتطبيق المحمول
 * CORS Setup for Mobile App
 */

echo "===============================================\n";
echo "        إعداد CORS للتطبيق المحمول\n";
echo "        CORS Setup for Mobile App\n";
echo "===============================================\n\n";

// ألوان للنص في Terminal
class Colors {
    public static $GREEN = "\033[0;32m";
    public static $RED = "\033[0;31m";
    public static $YELLOW = "\033[1;33m";
    public static $BLUE = "\033[0;34m";
    public static $NC = "\033[0m"; // No Color
}

function printStatus($message) {
    echo Colors::$GREEN . "✅ " . $message . Colors::$NC . "\n";
}

function printError($message) {
    echo Colors::$RED . "❌ " . $message . Colors::$NC . "\n";
}

function printWarning($message) {
    echo Colors::$YELLOW . "⚠️  " . $message . Colors::$NC . "\n";
}

function printInfo($message) {
    echo Colors::$BLUE . "ℹ️  " . $message . Colors::$NC . "\n";
}

// فحص ملف CORS الحالي
$corsConfigPath = 'config/cors.php';

printInfo("فحص إعدادات CORS الحالية...");

if (file_exists($corsConfigPath)) {
    printStatus("ملف CORS موجود");
    
    $corsContent = file_get_contents($corsConfigPath);
    
    // فحص الإعدادات المهمة
    if (strpos($corsContent, "'paths' => ['api/*'") !== false) {
        printStatus("مسارات API مُعرفة");
    } else {
        printWarning("مسارات API قد تحتاج تحديث");
    }
    
    if (strpos($corsContent, "'allowed_methods' => ['*']") !== false) {
        printStatus("جميع HTTP methods مسموحة");
    } else {
        printWarning("HTTP methods قد تحتاج تحديث");
    }
    
} else {
    printError("ملف CORS غير موجود");
}

echo "\n";

// إنشاء ملف CORS محسن للتطبيق المحمول
printInfo("إنشاء إعدادات CORS محسنة للتطبيق المحمول...");

$corsConfig = "<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or \"CORS\". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    */

    'paths' => ['api/*', 'sanctum/csrf-cookie'],

    'allowed_methods' => ['*'],

    'allowed_origins' => ['*'],

    'allowed_origins_patterns' => [],

    'allowed_headers' => ['*'],

    'exposed_headers' => [],

    'max_age' => 0,

    'supports_credentials' => false,

];
";

// حفظ الملف
if (file_put_contents($corsConfig, $corsConfig)) {
    printStatus("تم إنشاء ملف CORS محسن");
} else {
    printError("فشل في إنشاء ملف CORS");
}

echo "\n";

// فحص middleware CORS
printInfo("فحص middleware CORS...");

$kernelPath = 'app/Http/Kernel.php';
if (file_exists($kernelPath)) {
    $kernelContent = file_get_contents($kernelPath);
    
    if (strpos($kernelContent, 'HandleCors') !== false) {
        printStatus("CORS middleware مُفعل");
    } else {
        printWarning("CORS middleware قد يحتاج تفعيل");
        
        echo "\nأضف هذا السطر إلى \$middleware في app/Http/Kernel.php:\n";
        echo Colors::$BLUE . "\\Fruitcake\\Cors\\HandleCors::class," . Colors::$NC . "\n\n";
    }
} else {
    printError("ملف Kernel.php غير موجود");
}

// فحص إعدادات Sanctum
printInfo("فحص إعدادات Sanctum...");

$sanctumConfigPath = 'config/sanctum.php';
if (file_exists($sanctumConfigPath)) {
    printStatus("ملف Sanctum موجود");
    
    $sanctumContent = file_get_contents($sanctumConfigPath);
    
    if (strpos($sanctumContent, "'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS'") !== false) {
        printStatus("إعدادات Sanctum صحيحة");
    } else {
        printWarning("إعدادات Sanctum قد تحتاج تحديث");
    }
} else {
    printWarning("ملف Sanctum غير موجود");
}

echo "\n";

// إنشاء ملف اختبار API
printInfo("إنشاء ملف اختبار API...");

$testApiContent = "<?php

use Illuminate\\Http\\Request;
use Illuminate\\Support\\Facades\\Route;

/*
|--------------------------------------------------------------------------
| API Test Routes
|--------------------------------------------------------------------------
*/

Route::get('/test', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'API يعمل بشكل صحيح',
        'timestamp' => now(),
        'app_name' => config('app.name'),
        'app_url' => config('app.url'),
    ]);
});

Route::get('/test-cors', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'CORS يعمل بشكل صحيح',
        'cors_enabled' => true,
        'timestamp' => now(),
    ])->header('Access-Control-Allow-Origin', '*');
});
";

if (file_put_contents('routes/api-test.php', $testApiContent)) {
    printStatus("تم إنشاء ملف اختبار API");
    
    echo "\nيمكنك اختبار API من خلال:\n";
    echo Colors::$BLUE . "GET " . (env('APP_URL') ?: 'http://localhost') . "/api/test" . Colors::$NC . "\n";
    echo Colors::$BLUE . "GET " . (env('APP_URL') ?: 'http://localhost') . "/api/test-cors" . Colors::$NC . "\n\n";
} else {
    printError("فشل في إنشاء ملف اختبار API");
}

// نصائح إضافية
echo "\n";
printInfo("نصائح مهمة للتطبيق المحمول:");
echo "1. تأكد من أن الموقع يعمل على HTTPS\n";
echo "2. تأكد من تفعيل SSL Certificate\n";
echo "3. اختبر API endpoints من Postman أو المتصفح\n";
echo "4. تأكد من أن جميع routes تبدأ بـ /api/v1/\n";
echo "5. فعل rate limiting للحماية\n";

echo "\n";
printInfo("أوامر مفيدة:");
echo "php artisan config:clear\n";
echo "php artisan cache:clear\n";
echo "php artisan route:list --path=api\n";

echo "\n";
echo "===============================================\n";
printInfo("انتهى إعداد CORS للتطبيق المحمول");
echo "===============================================\n";
?>
