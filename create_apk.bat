@echo off
echo ===============================================
echo         Create APK File
echo ===============================================
echo.

echo Creating APK for: Zain Alabidin Services App
echo Connected to: https://www.zainalabidin.pro
echo.

echo Step 1: Check Flutter...
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed!
    echo Please install Flutter first
    pause
    exit /b 1
)

echo Flutter is ready!
echo.

echo Step 2: Navigate to app directory...
cd "التطبيق\User app and web"
if %errorlevel% neq 0 (
    echo ERROR: App directory not found
    echo Make sure you are in the correct folder
    pause
    exit /b 1
)

echo.
echo Step 3: Clean project...
flutter clean

echo.
echo Step 4: Get dependencies...
flutter pub get

echo.
echo Step 5: Building Release APK...
echo This may take 5-10 minutes...
echo.
flutter build apk --release

echo.
echo ===============================================

if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo SUCCESS: APK created successfully!
    echo.
    echo APK Location:
    echo build\app\outputs\flutter-apk\app-release.apk
    echo.
    
    for %%A in ("build\app\outputs\flutter-apk\app-release.apk") do (
        set "file_size=%%~zA"
        set /a "file_size_mb=!file_size! / 1024 / 1024"
        echo File Size: !file_size_mb! MB
    )
    
    echo.
    echo Next Steps:
    echo 1. Copy APK to your phone
    echo 2. Enable "Unknown Sources" in phone settings
    echo 3. Install the APK
    echo 4. The app will connect to: https://www.zainalabidin.pro
    echo.
    
    set /p open_folder="Open APK folder? (Y/N): "
    if /i "%open_folder%"=="y" (
        explorer "build\app\outputs\flutter-apk"
    )
    
) else (
    echo ERROR: Failed to create APK
    echo Check the errors above
    echo.
    echo Common solutions:
    echo 1. Make sure Flutter is properly installed
    echo 2. Check internet connection
    echo 3. Try: flutter doctor
)

echo.
echo ===============================================
echo App Details:
echo Name: Demandium (Zain Alabidin Services)
echo Website: https://www.zainalabidin.pro
echo Version: 3.2
echo ===============================================
pause
