-- ملف إعداد قاعدة البيانات
-- Database Setup SQL File

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS zainalabidin_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم قاعدة البيانات
CREATE USER IF NOT EXISTS 'zainalabidin_user'@'localhost' IDENTIFIED BY 'ZainAbidin2024!';

-- منح جميع الصلاحيات للمستخدم على قاعدة البيانات
GRANT ALL PRIVILEGES ON zainalabidin_db.* TO 'zainalabidin_user'@'localhost';

-- تطبيق التغييرات
FLUSH PRIVILEGES;

-- التحقق من إنشاء قاعدة البيانات
SHOW DATABASES LIKE 'zainalabidin_db';

-- التحقق من المستخدم
SELECT User, Host FROM mysql.user WHERE User = 'zainalabidin_user';

-- رسالة تأكيد
SELECT 'تم إنشاء قاعدة البيانات والمستخدم بنجاح!' as 'Status';
SELECT 'Database and user created successfully!' as 'Status_EN';

-- معلومات الاتصال
SELECT 'استخدم هذه المعلومات في ملف .env:' as 'Instructions';
SELECT 'DB_DATABASE=zainalabidin_db' as 'Database_Name';
SELECT 'DB_USERNAME=zainalabidin_user' as 'Username';
SELECT 'DB_PASSWORD=ZainAbidin2024!' as 'Password';
