name: demandium
description: A new Flutter application.
publish_to: 'none' #
version: 1.0.8+11

environment:
  sdk: ^3.5.3

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  get: ^4.6.6
  intl: ^0.19.0
  shared_preferences: ^2.3.5
  connectivity_plus: ^6.1.1
  firebase_core: ^3.12.1
  firebase_messaging: ^15.2.4
  path_provider: ^2.1.5
  url_strategy: ^0.3.0
  image_picker: ^1.1.2
  cached_network_image: ^3.4.1
  google_maps_flutter: ^2.10.1
  geolocator: ^13.0.2
  url_launcher: ^6.3.1
  country_code_picker: 3.1.0
  phone_numbers_parser: ^9.0.3
  webview_flutter: ^4.10.0
  pin_code_fields: ^8.0.1
  flutter_widget_from_html_core: ^0.16.0
  flutter_typeahead: ^5.2.0
  pointer_interceptor: ^0.10.1+2
  shimmer_animation: ^2.2.2
  carousel_slider: ^5.0.0
  google_sign_in: ^6.2.2
  flutter_facebook_auth: ^6.0.4
  smooth_page_indicator: ^1.2.0+3
  expandable: ^5.0.1
  file_picker: ^9.0.2
  flutter_html: ^3.0.0-alpha.6
  flutter_local_notifications: ^18.0.1
  universal_html: ^2.2.4
  flutter_inappwebview: ^6.1.5
  timeago: ^3.7.0
  pdf: ^3.11.1
  diffutil_dart: ^4.0.1
  flutter_downloader: ^1.12.0
  permission_handler: ^11.4.0
  fluttertoast: ^8.2.10
  printing: ^5.13.4
  audioplayers: ^6.2.0
  dotted_border: ^2.1.0
  expandable_bottom_sheet: ^1.1.1+1
  share_plus: ^10.1.3
  rect_getter: ^1.1.0
  scroll_to_index: any
  http_parser: ^4.1.2
  http: ^1.2.2
  path: ^1.9.0
  syncfusion_flutter_datepicker: ^28.2.9
  sign_in_with_apple: ^6.1.4
  uuid: ^4.5.1
  just_the_tooltip: ^0.0.12
  custom_info_window: ^1.0.1
  custom_marker: ^1.0.0
  photo_view: ^0.15.0
  flutter_slidable: ^4.0.0
  app_links: ^6.3.2
  syncfusion_flutter_sliders: ^28.2.9
  syncfusion_flutter_core: ^28.2.9
  flutter_xlider: ^3.5.0
  video_player: ^2.9.3
  chewie: ^1.10.0
  firebase_auth: ^5.5.1
  readmore: ^3.0.0
  lottie: ^3.3.1
  drift: ^2.25.1
  drift_flutter: ^0.2.4



dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  drift_dev: ^2.19.0
  build_runner: ^2.4.13

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/language/
    - assets/json/
    - assets/map/
    - assets/

  fonts:
    - family: Ubuntu
      fonts:
        - asset: assets/font/Roboto-Light.ttf
          weight: 300
        - asset: assets/font/Roboto-Regular.ttf
          weight: 400
        - asset: assets/font/Roboto-Medium.ttf
          weight: 500
        - asset: assets/font/Roboto-Bold.ttf
          weight: 700
