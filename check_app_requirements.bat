@echo off
chcp 65001 >nul
echo ===============================================
echo        فحص متطلبات تشغيل التطبيق
echo       Check App Requirements
echo ===============================================
echo.

echo 1️⃣ فحص Flutter...
flutter --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Flutter مثبت
    flutter --version
) else (
    echo ❌ Flutter غير مثبت
    echo 📥 حمل Flutter من: https://flutter.dev/docs/get-started/install/windows
    echo.
    echo خطوات التثبيت:
    echo 1. حمل Flutter SDK
    echo 2. استخرج الملفات إلى C:\flutter
    echo 3. أضف C:\flutter\bin إلى PATH
    echo 4. شغل: flutter doctor
)

echo.
echo 2️⃣ فحص Android Studio...
if exist "C:\Program Files\Android\Android Studio" (
    echo ✅ Android Studio مثبت
) else if exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
    echo ✅ Android SDK موجود
) else (
    echo ❌ Android Studio غير مثبت
    echo 📥 حمل من: https://developer.android.com/studio
)

echo.
echo 3️⃣ فحص ملفات التطبيق...
if exist "التطبيق\User app and web\pubspec.yaml" (
    echo ✅ ملفات التطبيق موجودة
) else (
    echo ❌ ملفات التطبيق غير موجودة
    echo تأكد من وجود مجلد "التطبيق\User app and web"
)

echo.
echo 4️⃣ فحص اتصال الإنترنت...
ping -n 1 google.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ الإنترنت متصل
) else (
    echo ❌ لا يوجد اتصال بالإنترنت
)

echo.
echo 5️⃣ فحص اتصال الموقع...
curl -s -o nul -w "%%{http_code}" https://www.zainalabidin.pro > temp_result.txt 2>nul
if exist temp_result.txt (
    set /p RESULT=<temp_result.txt
    if "!RESULT!"=="200" (
        echo ✅ الموقع يعمل بشكل صحيح
    ) else (
        echo ⚠️ الموقع يعطي كود: !RESULT!
    )
    del temp_result.txt
) else (
    echo ⚠️ لا يمكن الوصول للموقع
)

echo.
echo ===============================================
echo 📋 ملخص الفحص:
echo.

REM فحص شامل
set "all_good=true"

flutter --version >nul 2>&1
if %errorlevel% neq 0 set "all_good=false"

if not exist "التطبيق\User app and web\pubspec.yaml" set "all_good=false"

if "%all_good%"=="true" (
    echo ✅ جميع المتطلبات متوفرة!
    echo 🚀 يمكنك تشغيل التطبيق الآن
    echo.
    echo شغل: run_mobile_app.bat
) else (
    echo ❌ بعض المتطلبات مفقودة
    echo يرجى تثبيت المتطلبات المفقودة أولاً
)

echo.
echo ===============================================
pause
