#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تحديث إعدادات التطبيق
Mobile App Configuration Update Script
"""

import os
import re
import sys

def print_colored(text, color='white'):
    """طباعة نص ملون"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'purple': '\033[95m',
        'cyan': '\033[96m',
        'white': '\033[97m',
        'end': '\033[0m'
    }
    print(f"{colors.get(color, colors['white'])}{text}{colors['end']}")

def update_base_url(file_path, new_url):
    """تحديث baseUrl في ملف app_constants.dart"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # البحث عن السطر المطلوب وتحديثه
        pattern = r"static const String baseUrl = '[^']*';"
        replacement = f"static const String baseUrl = '{new_url}';"
        
        if re.search(pattern, content):
            updated_content = re.sub(pattern, replacement, content)
            
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            
            print_colored("✅ تم تحديث baseUrl بنجاح", 'green')
            return True
        else:
            print_colored("❌ لم يتم العثور على baseUrl في الملف", 'red')
            return False
            
    except FileNotFoundError:
        print_colored(f"❌ الملف غير موجود: {file_path}", 'red')
        return False
    except Exception as e:
        print_colored(f"❌ خطأ في تحديث الملف: {str(e)}", 'red')
        return False

def update_google_maps_key(file_path, api_key):
    """تحديث Google Maps API Key في AndroidManifest.xml"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # البحث عن السطر المطلوب وتحديثه
        pattern = r'android:value="YOUR_MAP_KEY"'
        replacement = f'android:value="{api_key}"'
        
        if 'YOUR_MAP_KEY' in content:
            updated_content = content.replace('android:value="YOUR_MAP_KEY"', f'android:value="{api_key}"')
            
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            
            print_colored("✅ تم تحديث Google Maps API Key بنجاح", 'green')
            return True
        else:
            print_colored("⚠️ Google Maps API Key محدث بالفعل أو غير موجود", 'yellow')
            return True
            
    except FileNotFoundError:
        print_colored(f"❌ الملف غير موجود: {file_path}", 'red')
        return False
    except Exception as e:
        print_colored(f"❌ خطأ في تحديث الملف: {str(e)}", 'red')
        return False

def update_app_name(file_path, app_name):
    """تحديث اسم التطبيق في app_constants.dart"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # البحث عن السطر المطلوب وتحديثه
        pattern = r"static const String appName = '[^']*';"
        replacement = f"static const String appName = '{app_name}';"
        
        if re.search(pattern, content):
            updated_content = re.sub(pattern, replacement, content)
            
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            
            print_colored("✅ تم تحديث اسم التطبيق بنجاح", 'green')
            return True
        else:
            print_colored("❌ لم يتم العثور على appName في الملف", 'red')
            return False
            
    except Exception as e:
        print_colored(f"❌ خطأ في تحديث اسم التطبيق: {str(e)}", 'red')
        return False

def main():
    print_colored("=" * 50, 'cyan')
    print_colored("        تحديث إعدادات التطبيق", 'cyan')
    print_colored("    Mobile App Configuration Update", 'cyan')
    print_colored("=" * 50, 'cyan')
    print()
    
    # مسارات الملفات
    app_constants_path = "التطبيق/User app and web/lib/utils/app_constants.dart"
    android_manifest_path = "التطبيق/User app and web/android/app/src/main/AndroidManifest.xml"
    
    # التحقق من وجود الملفات
    if not os.path.exists(app_constants_path):
        print_colored(f"❌ ملف app_constants.dart غير موجود في: {app_constants_path}", 'red')
        print_colored("تأكد من أن مجلد التطبيق في المكان الصحيح", 'yellow')
        return
    
    # طلب المعلومات من المستخدم
    print_colored("يرجى إدخال المعلومات التالية:", 'blue')
    print()
    
    # رابط الموقع
    website_url = input("🌐 أدخل رابط موقعك (مثال: https://yourdomain.com): ").strip()
    if not website_url:
        print_colored("❌ يجب إدخال رابط الموقع", 'red')
        return
    
    # إزالة / من نهاية الرابط إذا وجدت
    if website_url.endswith('/'):
        website_url = website_url[:-1]
    
    # اسم التطبيق (اختياري)
    app_name = input("📱 أدخل اسم التطبيق (اتركه فارغاً للاحتفاظ بالاسم الحالي): ").strip()
    
    # Google Maps API Key (اختياري)
    maps_key = input("🗺️ أدخل Google Maps API Key (اتركه فارغاً لتخطي): ").strip()
    
    print()
    print_colored("بدء تحديث الإعدادات...", 'yellow')
    print()
    
    # تحديث baseUrl
    success = update_base_url(app_constants_path, website_url)
    
    # تحديث اسم التطبيق إذا تم إدخاله
    if app_name:
        update_app_name(app_constants_path, app_name)
    
    # تحديث Google Maps API Key إذا تم إدخاله
    if maps_key and os.path.exists(android_manifest_path):
        update_google_maps_key(android_manifest_path, maps_key)
    elif maps_key:
        print_colored(f"⚠️ ملف AndroidManifest.xml غير موجود في: {android_manifest_path}", 'yellow')
    
    print()
    if success:
        print_colored("🎉 تم تحديث إعدادات التطبيق بنجاح!", 'green')
        print()
        print_colored("الخطوات التالية:", 'blue')
        print_colored("1. تأكد من أن موقعك يعمل على الرابط المحدد", 'white')
        print_colored("2. تأكد من تفعيل HTTPS", 'white')
        print_colored("3. تأكد من تفعيل CORS في الموقع", 'white')
        print_colored("4. اختبر التطبيق: flutter run", 'white')
        print_colored("5. ابني التطبيق للإنتاج: flutter build apk --release", 'white')
    else:
        print_colored("❌ فشل في تحديث بعض الإعدادات", 'red')
    
    print()
    print_colored("=" * 50, 'cyan')

if __name__ == "__main__":
    main()
