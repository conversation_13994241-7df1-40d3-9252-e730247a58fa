@echo off
echo ===============================================
echo         Fix Google Maps Issue
echo ===============================================
echo.

echo Problem: Google Maps not loading correctly
echo Solution: Setup Google Maps API Key
echo.

echo Step 1: Clear cache...
php artisan cache:clear
php artisan config:clear
php artisan view:clear

echo.
echo Step 2: Setup Google Maps in database...
mysql -u kidzrcle_kalfa -p kidzrcle_kalfa < setup_google_maps.sql

echo.
echo ===============================================
echo Next Steps:
echo.
echo 1. Get Google Maps API Key from:
echo    https://console.cloud.google.com/
echo.
echo 2. Go to admin panel:
echo    https://www.zainalabidin.pro/admin
echo.
echo 3. Navigate to:
echo    Settings > Third Party > Google Map
echo.
echo 4. Enter your API keys:
echo    - Map API Key Server: YOUR_SERVER_KEY
echo    - Map API Key Client: YOUR_CLIENT_KEY
echo.
echo 5. Click Update
echo.
echo 6. Test the maps at:
echo    https://www.zainalabidin.pro/provider/auth/sign-up
echo.
echo ===============================================
echo.
echo For detailed instructions, read:
echo GOOGLE_MAPS_SETUP_GUIDE.md
echo.
echo ===============================================
pause
