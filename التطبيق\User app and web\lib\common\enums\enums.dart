enum SocialLoginType { google, facebook, apple}

enum LoginMedium { manual, otp, social}

enum DiscountType {general, coupon, campaign, refer}

enum SendOtpType {forgetPassword, firebase, verification}

enum EditProfileTabControllerState {generalInfo,accountIno}
enum ToasterMessageType {success, error, info}
enum RepeatBookingType {daily, weekly, custom}

enum ServiceType { all ,regular, repeat}
enum ServiceLocationType { customer, provider}

enum HtmlType {
  termsAndCondition,
  aboutUs,
  privacyPolicy,
  cancellationPolicy,
  refundPolicy
}

enum DataSourceEnum {client, local}
enum LocalCachesTypeEnum{all, app, web, none}
enum ApiMethodType {get, post}